// 合作农户电站组件
const FarmerStationManagement = {
    template: `
        <div class="station-management">
            <!-- 工具栏 -->
            <div class="toolbar" style="margin-bottom: 20px;">
                <el-form :inline="true" :model="searchForm">
                    <el-form-item>
                        <el-input v-model="searchForm.keyword" placeholder="电站名称/地址/联系人" clearable></el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-select v-model="searchForm.department_id" placeholder="选择部门" clearable>
                            <el-option
                                v-for="item in departmentOptions"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-select v-model="searchForm.status" placeholder="运行状态" clearable>
                            <el-option label="正常" :value="1"></el-option>
                            <el-option label="停用" :value="0"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="handleSearch">搜索</el-button>
                        <el-button @click="resetSearch">重置</el-button>
                        <el-button type="primary" @click="showCreateDialog">新增农户电站</el-button>
                        <el-button type="primary" @click="handleImport">导入农户电站数据</el-button>
                    </el-form-item>
                </el-form>
            </div>

            <!-- 电站列表 -->
            <el-table 
                v-loading="loading"
                element-loading-text="加载中..."
                element-loading-spinner="el-icon-loading"
                element-loading-background="rgba(255, 255, 255, 0.8)"
                :data="tableData" 
                border 
                style="width: 100%">
                <el-table-column prop="total_serial" label="总序号" width="80" align="center"></el-table-column>
                <el-table-column prop="department.name" label="所属部门" min-width="120"></el-table-column>
                <el-table-column prop="gongwang_account" label="国网号" min-width="150"></el-table-column>
                <el-table-column prop="contact_name" label="联系人" width="120"></el-table-column>
                <el-table-column prop="contact_phone" label="联系电话" width="120"></el-table-column>
                <el-table-column prop="address" label="电站地址" min-width="200"></el-table-column>
                <el-table-column prop="capacity" label="装机容量" width="120">
                    <template slot-scope="scope">
                        {{ scope.row.capacity }} kW
                    </template>
                </el-table-column>
                <el-table-column prop="component_brand" label="组件品牌" width="120"></el-table-column>
                <el-table-column prop="component_count" label="组件数量" width="120"></el-table-column>
                <el-table-column label="状态" width="100">
                    <template slot-scope="scope">
                        <el-tag :type="scope.row.status === 1 ? 'success' : 'info'">
                            {{ scope.row.status === 1 ? '正常' : '停用' }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="280" fixed="right">
                    <template slot-scope="scope">
                        <el-button size="small" type="primary" @click="showEditDialog(scope.row)">编辑</el-button>
                        <el-button size="small" type="success" @click="showArchiveDialog(scope.row)">档案</el-button>
                        <el-button size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="pagination" style="margin-top: 20px; text-align: right;">
                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page.sync="page"
                    :page-sizes="[10, 20, 50, 100]"
                    :page-size="limit"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total">
                </el-pagination>
            </div>

            <!-- 表单对话框 -->
            <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="600px" @close="$refs.form.resetFields()">
                <el-form :model="form" :rules="rules" ref="form" label-width="100px">
                    <el-form-item label="总序号" prop="total_serial">
                        <el-input v-model="form.total_serial" placeholder="请输入总序号"></el-input>
                    </el-form-item>
                    <el-form-item label="原部室">
                        <el-input v-model="form.original_department" placeholder="请输入原部室（选填）"></el-input>
                    </el-form-item>
                    <el-form-item label="项目建设年份">
                        <el-date-picker
                            v-model="form.construction_year"
                            type="year"
                            placeholder="选择项目建设年份（选填）"
                            format="yyyy"
                            value-format="yyyy"
                            clearable>
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item label="安装日期">
                        <el-date-picker
                            v-model="form.install_date"
                            type="date"
                            placeholder="选择安装日期（选填）"
                            format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd"
                            clearable>
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item label="国网号" prop="gongwang_account">
                        <el-input v-model="form.gongwang_account"></el-input>
                    </el-form-item>
                    <el-form-item label="联系人" prop="contact_name">
                        <el-input v-model="form.contact_name"></el-input>
                    </el-form-item>
                    <el-form-item label="联系电话" prop="contact_phone">
                        <el-input v-model="form.contact_phone"></el-input>
                    </el-form-item>
                    <el-form-item label="电站地址" prop="address">
                        <el-input v-model="form.address" placeholder="请输入电站地址"></el-input>
                    </el-form-item>
                    <el-form-item label="装机容量(kW)" prop="capacity">
                        <el-input-number v-model="form.capacity" :min="0" :precision="2" :step="0.5" placeholder="请输入装机容量"></el-input-number>
                    </el-form-item>
                    <el-form-item label="组件品牌" prop="component_brand">
                        <el-input v-model="form.component_brand"></el-input>
                    </el-form-item>
                    <el-form-item label="组件数量" prop="component_count">
                        <el-input v-model="form.component_count"></el-input>
                    </el-form-item>
                    <el-form-item label="异常电站备注">
                        <el-input
                            v-model="form.abnormal_remark"
                            type="textarea"
                            :rows="3"
                            placeholder="请输入异常电站备注（如：拆除、资产减除、电站更换等）">
                        </el-input>
                    </el-form-item>
                    <el-form-item label="所属部门" prop="department_id">
                        <el-select v-model="form.department_id" placeholder="选择部门">
                            <el-option
                                v-for="item in departmentOptions"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="状态">
                        <el-switch
                            v-model="form.status"
                            :active-value="1"
                            :inactive-value="0">
                        </el-switch>
                    </el-form-item>
                </el-form>
                <div slot="footer" class="dialog-footer">
                    <el-button @click="dialogVisible = false">取 消</el-button>
                    <el-button type="primary" @click="handleSubmit">确 定</el-button>
                </div>
            </el-dialog>

            <!-- 导入对话框 -->
            <el-dialog title="导入农户电站数据" :visible.sync="importDialogVisible" width="600px">
                <el-upload
                    ref="importUpload"
                    action=""
                    :auto-upload="false"
                    :on-change="handleImportFileChange"
                    :file-list="importFileList"
                    accept=".xlsx, .xls">
                    <el-button slot="trigger" type="primary">选取文件</el-button>
                    <el-button 
                        type="success" 
                        @click="submitImport" 
                        :loading="importing"
                        :disabled="importing">
                        {{ importing ? (importProgress < 30 ? '正在上传...' : '正在处理数据...') : '开始导入' }}
                    </el-button>
                    <div slot="tip" class="el-upload__tip">只能上传xlsx/xls文件，支持大数据量导入（15000+行），建议文件大小不超过100MB</div>
                </el-upload>
                <el-progress
                    v-if="importProgress > 0"
                    :percentage="importProgress"
                    :status="importStatus"
                    :show-text="true"
                    :format="formatImportProgress">
                </el-progress>
            </el-dialog>

            <!-- 档案管理对话框 -->
            <el-dialog 
                title="档案管理" 
                :visible.sync="archiveDialogVisible" 
                fullscreen 
                :show-close="true"
                custom-class="archive-dialog"
                @closed="handleArchiveDialogClose">
                <div v-loading="loading" 
                     element-loading-text="加载中..."
                     element-loading-spinner="el-icon-loading"
                     element-loading-background="rgba(255, 255, 255, 0.8)"
                     style="height: 100%">
                    <div class="archive-header">
                        <div class="header-left">
                            <span class="station-name">{{ currentStation ? currentStation.contact_name : '' }}</span>
                            <el-tag size="small" type="info">共 {{ archiveFiles.length }} 个文件</el-tag>
                        </div>
                        <div class="header-right">
                            <el-button type="primary" size="small" icon="el-icon-upload2" @click="showUploadDialog">上传档案</el-button>
                        </div>
                    </div>
                    <el-container class="archive-container">
                        <el-aside width="280px" class="archive-left">
                            <el-menu
                                v-if="archiveFiles.length > 0"
                                :default-active="activeFileId"
                                @select="handleFileSelect">
                                <el-menu-item 
                                    v-for="file in archiveFiles" 
                                    :key="file.id"
                                    :index="file.id">
                                    <i :class="getFileIcon(file.type)"></i>
                                    <span slot="title" class="file-name">{{ file.name }}</span>
                                </el-menu-item>
                            </el-menu>
                            <div v-else class="no-files">
                                <i class="el-icon-folder-opened"></i>
                                <p>暂无档案文件</p>
                                <el-button type="text" @click="showUploadDialog">立即上传</el-button>
                            </div>
                        </el-aside>
                        <el-main class="archive-right">
                            <div v-if="!selectedFile" class="no-preview">
                                <i class="el-icon-document"></i>
                                <p>请选择要查看的档案</p>
                            </div>
                            <div v-else class="preview-container">
                                <div class="preview-header">
                                    <div class="file-title">
                                        <i :class="getFileIcon(selectedFile.type)"></i>
                                        <span>{{ selectedFile.name }}</span>
                                    </div>
                                    <div class="file-meta">
                                        <span>大小：{{ formatFileSize(selectedFile.file_size) }}</span>
                                        <span>上传时间：{{ formatDate(selectedFile.create_at) }}</span>
                                    </div>
                                    <el-button 
                                        type="danger" 
                                        size="small" 
                                        icon="el-icon-delete" 
                                        class="delete-button"
                                        @click="handleDeleteArchive(selectedFile)">删除文件</el-button>
                                </div>
                                <div class="preview-content">
                                    <div v-if="selectedFile.type === 'image'" class="image-preview">
                                    <img :src="selectedFile.url" alt="预览图片">
                                    </div>
                                    <div v-else-if="selectedFile.type === 'pdf'" class="pdf-preview">
                                        <iframe :src="selectedFile.url"></iframe>
                                    </div>
                                </div>
                            </div>
                        </el-main>
                    </el-container>
                </div>
            </el-dialog>

            <!-- 档案上传对话框 -->
            <el-dialog title="上传档案" :visible.sync="uploadDialogVisible" width="500px">
                <el-form :model="uploadForm" :rules="uploadRules" ref="uploadForm">
                    <el-upload
                        ref="upload"
                        action=""
                        :auto-upload="false"
                        :on-change="handleFileChange"
                        :on-remove="handleFileRemove"
                        :file-list="uploadFileList"
                        multiple
                        accept=".jpg,.jpeg,.png,.pdf">
                        <el-button slot="trigger" type="primary">选择文件</el-button>
                    </el-upload>
                    <div v-for="(file, index) in uploadFileList" :key="file.uid" class="upload-item">
                        <el-form-item 
                            :label="file.name"
                            :prop="'fileNames.' + index"
                            :rules="{ required: true, message: '请输入文件名称', trigger: 'blur' }">
                            <el-input v-model="uploadForm.fileNames[index]" placeholder="请输入文件名称"></el-input>
                        </el-form-item>
                        <el-progress 
                            v-if="file.progress > 0" 
                            :percentage="file.progress"
                            :status="file.status">
                        </el-progress>
                    </div>
                </el-form>
                <div slot="footer">
                    <el-button @click="uploadDialogVisible = false">取 消</el-button>
                    <el-button type="primary" @click="submitUpload" :loading="uploading">确 定</el-button>
                </div>
            </el-dialog>
        </div>

        <!-- 导入错误详情对话框样式 -->
        <style>
        .import-error-dialog .el-message-box {
            width: 80% !important;
            max-width: 1000px !important;
        }
        .import-error-dialog .el-message-box__content {
            max-height: 600px !important;
            overflow-y: auto !important;
        }
        .import-error-dialog table {
            font-size: 12px !important;
            margin-top: 10px !important;
        }
        .import-error-dialog th, .import-error-dialog td {
            text-align: left !important;
            word-break: break-all !important;
        }
        </style>
    `,
    mixins: [StationManagement],
    data() {
        return {
            stationType: 1, // 固定为合作农户类型
            loading: false, // 添加列表加载状态
            searchForm: {
                keyword: '',
                department_id: '',
                status: ''
            },
            rules: {
                gongwang_account: [
                    { required: true, message: '请输入国网号', trigger: 'blur' }
                ],
                contact_name: [
                    { required: true, message: '请输入联系人', trigger: 'blur' }
                ],
                contact_phone: [
                    { required: true, message: '请输入联系电话', trigger: 'blur' },
                    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
                ],
                address: [
                    { required: true, message: '请输入电站地址', trigger: 'blur' }
                ],
                capacity: [
                    { required: true, message: '请输入装机容量', trigger: 'blur' }
                ],
                component_brand: [
                    { required: true, message: '请输入组件品牌', trigger: 'blur' }
                ],
                department_id: [
                    { required: true, message: '请选择所属部门', trigger: 'change' }
                ]
            },
            uploadProgress: 0,
            uploadStatus: '',
            importDialogVisible: false,
            importFileList: [],
            importProgress: 0,
            importStatus: '',
            importing: false,
            archiveDialogVisible: false,
            archiveFiles: [],
            selectedFile: null,
            activeFileId: '',
            uploadDialogVisible: false,
            uploadFileList: [],
            uploading: false,
            uploadForm: {
                fileNames: []
            },
            uploadRules: {
                fileNames: [{
                    required: true,
                    message: '请输入文件名称',
                    trigger: 'blur'
                }]
            },
            loading: false,
            uploading: false,
            currentStation: null,  // 添加当前选中的电站
        }
    },
    created() {
        this.fetchData();
        this.fetchDepartments();
    },
    methods: {
        // 获取电站列表数据
        fetchData() {
            this.loading = true;
            const params = {
                page: this.page,
                limit: this.limit,
                type: this.stationType,
                keyword: this.searchForm.keyword || '',
                department_id: this.searchForm.department_id || '',
                status: this.searchForm.status !== '' ? this.searchForm.status : null
            };
            
            console.log('合作农户电站 - 搜索参数:', params); // 调试日志
            
            this.$http.get('/station', { params })
                .then(response => {
                    console.log('API返回的原始数据:', response); // 添加调试信息
                    if (response.data.code === 200) {
                        this.tableData = response.data.data.items || [];
                        this.total = response.data.data.total || 0;
                        console.log('加载的电站数量:', this.tableData.length); // 调试日志
                    } else {
                        this.$message.error(response.data.message || '获取数据失败');
                    }
                })
                .catch(error => {
                    console.error('获取电站数据失败:', error);
                    this.$message.error('获取电站数据失败');
                })
                .finally(() => {
                    this.loading = false;
                });
        },
        // 处理搜索
        handleSearch() {
            this.page = 1;
            this.fetchData();
            console.log('执行搜索，搜索条件:', this.searchForm);
        },

        // 重置搜索
        resetSearch() {
            this.searchForm = {
                keyword: '',
                department_id: '',
                status: ''
            };
            this.page = 1;
            this.fetchData();
            console.log('重置搜索条件并刷新数据');
        },
        
        // 显示创建对话框
        showCreateDialog() {
            // 确保部门列表已加载
            this.fetchDepartments().then(() => {
                // 获取当前日期并格式化为YYYY-MM-DD
                const today = new Date();
                const installDate = today.getFullYear() + '-' + 
                    String(today.getMonth() + 1).padStart(2, '0') + '-' + 
                    String(today.getDate()).padStart(2, '0');
                
                this.form = {
                    id: '',
                    type: this.stationType,
                    total_serial: '', // 总序号字段
                    original_department: '', // 原部室字段
                    construction_year: '', // 项目建设年份字段
                    install_date: installDate, // 安装日期，默认今天
                    gongwang_account: '',
                    contact_name: '',
                    contact_phone: '',
                    address: '',
                    capacity: 0,
                    component_brand: '',
                    component_count: '',
                    abnormal_remark: '', // 异常电站备注字段
                    department_id: '',
                    longitude: 0, // 默认经度设为0
                    latitude: 0,  // 默认纬度设为0
                    status: 1
                };
                console.log('新增电站 - 可选部门列表:', this.departmentOptions);
                console.log('新增电站 - departmentOptions类型:', typeof this.departmentOptions, Array.isArray(this.departmentOptions));
                this.dialogVisible = true;
                this.dialogTitle = '新增合作农户电站';
            });
        },
        
        // 显示编辑对话框
        showEditDialog(row) {
            // 确保部门列表已加载
            this.fetchDepartments().then(() => {
                // 创建数据副本，避免直接修改表格数据
                this.form = {
                    id: row.id,
                    type: row.type,
                    total_serial: row.total_serial || '', // 总序号字段
                    original_department: row.original_department || '', // 原部室字段
                    construction_year: row.construction_year || '', // 项目建设年份字段
                    install_date: row.install_date || '', // 安装日期字段
                    gongwang_account: row.gongwang_account,
                    contact_name: row.contact_name,
                    contact_phone: row.contact_phone,
                    address: row.address,
                    capacity: row.capacity,
                    component_brand: row.component_brand,
                    component_count: row.component_count,
                    abnormal_remark: row.abnormal_remark || '', // 异常电站备注字段
                    department_id: row.department_id || (row.department ? row.department.id : ''),
                    // 确保经纬度为数值，如果为空或无效则设为0
                    longitude: Number(row.longitude) || 0,
                    latitude: Number(row.latitude) || 0,
                    status: row.status
                };
                console.log('编辑电站 - 已选择的部门ID:', this.form.department_id);
                console.log('编辑电站 - 可选部门列表:', this.departmentOptions);
                console.log('编辑电站 - departmentOptions类型:', typeof this.departmentOptions, Array.isArray(this.departmentOptions));
                
                this.dialogVisible = true;
                this.dialogTitle = '编辑合作农户电站';
            });
        },

        // 处理表单提交
        handleSubmit() {
            this.$refs.form.validate(valid => {
                if (valid) {
                    const isEdit = !!this.form.id;
                    const method = isEdit ? 'put' : 'post';
                    const url = isEdit ? `/station/${this.form.id}` : '/station';
                    
                    // 获取当前日期并格式化为YYYY-MM-DD
                    const today = new Date();
                    const installDate = today.getFullYear() + '-' + 
                        String(today.getMonth() + 1).padStart(2, '0') + '-' + 
                        String(today.getDate()).padStart(2, '0');
                    
                    // 添加电站类型、站名和安装日期，并确保经纬度为数值
                    const data = {
                        ...this.form,
                        type: this.stationType,
                        station_name: this.form.contact_name, // 将station_name设置为contact_name的值
                        // 安装日期可为空，为空时不设默认值
                        install_date: this.form.install_date || null,
                        latitude: this.form.latitude || 0, // 纬度为空时设为0
                        longitude: this.form.longitude || 0, // 经度为空时设为0
                    };
                    
                    // 确保latitude和longitude字段为数值类型
                    data.latitude = Number(data.latitude) || 0;
                    data.longitude = Number(data.longitude) || 0;
                    
                    console.log('添加必要字段后的提交数据:', data);
                    
                    this.$http[method](url, data)
                        .then(response => {
                            if (response.data.code === 200) {
                                this.$message.success(isEdit ? '电站更新成功' : '电站创建成功');
                                this.dialogVisible = false;
                                this.fetchData();
                            } else {
                                this.$message.error(response.data.message);
                                console.error('API错误响应:', response.data);
                            }
                        })
                        .catch(error => {
                            console.error('操作失败:', error);
                            this.$message.error('操作失败');
                        });
                }
            });
        },

        // 处理导入按钮点击
        handleImport() {
            this.importDialogVisible = true;
        },

        // 处理文件选择改变
        handleImportFileChange(file, fileList) {
            this.importFileList = fileList.slice(-1);
        },

        // 提交导入
        submitImport() {
            const file = this.importFileList[0];
            if (!file) {
                this.$message.error('请选择文件');
                return;
            }

            const formData = new FormData();
            formData.append('file', file.raw);
            formData.append('type', 1);  // 农户电站类型为1

            this.importProgress = 0;
            this.importStatus = '';
            this.importing = true;  // 设置导入状态为true

            this.$http.post('/station/import', formData, {
                headers: {
                    'Content-Type': 'multipart/form-data'
                },
                timeout: 600000, // 10分钟超时，适应大数据量导入
                onUploadProgress: progressEvent => {
                    // 上传进度占总进度的30%
                    this.importProgress = Math.round((progressEvent.loaded * 30) / progressEvent.total);
                    this.importStatus = 'active';
                },
                onDownloadProgress: progressEvent => {
                    // 处理进度占总进度的70%，从30%开始
                    if (progressEvent.lengthComputable) {
                        this.importProgress = 30 + Math.round((progressEvent.loaded * 70) / progressEvent.total);
                    } else {
                        // 如果无法计算进度，显示处理中状态
                        this.importProgress = 50;
                    }
                    this.importStatus = 'active';
                }
            }).then(response => {
                if (response.data.code === 200) {
                    this.importProgress = 100;
                    this.importStatus = 'success';

                    // 显示详细的导入结果
                    const data = response.data.data;
                    if (data && (data.success || data.fail)) {
                        const message = `导入完成！成功：${data.success}条，失败：${data.fail}条`;
                        this.$message.success(message);

                        // 如果有错误，显示错误详情和修复建议
                        if (data.errors && data.errors.length > 0) {
                            this.showImportErrorDetails(data);
                        }
                    } else {
                        this.$message.success(response.data.message || '导入成功');
                    }
                    this.fetchData();
                } else {
                    this.importStatus = 'exception';
                    this.$message.error(response.data.message);
                }
                setTimeout(() => {
                    this.importDialogVisible = false;
                    this.importProgress = 0;
                    this.importStatus = '';
                    this.importFileList = [];
                    this.importing = false;  // 重置导入状态
                }, 1500);
            }).catch(error => {
                this.importStatus = 'exception';
                console.error('导入失败:', error);

                // 根据错误类型提供不同的提示
                let errorMessage = '导入失败';
                if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
                    errorMessage = '导入超时。对于15000+行大数据量，建议：1) 检查网络连接稳定性 2) 如仍超时可分批导入 3) 联系技术支持';
                } else if (error.response && error.response.data && error.response.data.message) {
                    errorMessage = '导入失败: ' + error.response.data.message;
                } else if (error.message) {
                    errorMessage = '导入失败: ' + error.message;
                }

                this.$message.error(errorMessage);
                setTimeout(() => {
                    this.importDialogVisible = false;
                    this.importProgress = 0;
                    this.importStatus = '';
                    this.importFileList = [];
                    this.importing = false;  // 重置导入状态
                }, 3000); // 延长显示时间以便用户阅读错误信息
            });
        },

        // 显示档案管理对话框
        showArchiveDialog(row) {
            this.currentStation = row;
            this.archiveDialogVisible = true;
            this.selectedFile = null;
            this.archiveFiles = [];
            this.loading = true;
            
            this.fetchArchiveFiles().finally(() => {
                this.loading = false;
            });
        },

        // 获取档案文件列表
        fetchArchiveFiles() {
            if (!this.currentStation) return;
            
            return this.$http.get(`/asset/${this.currentStation.id}/archives`)
                .then(response => {
                    if (response.data.code === 200) {
                        this.archiveFiles = response.data.data;
                    } else {
                        this.$message.error(response.data.message);
                    }
                })
                .catch(error => {
                    console.error('获取档案列表失败:', error);
                    this.$message.error('获取档案列表失败');
                })
                .finally(() => {
                    this.loading = false;
                });
        },

        handleFileSelect(fileId) {
            this.activeFileId = fileId;
            this.selectedFile = this.archiveFiles.find(file => file.id === fileId);
        },

        // 显示档案上传对话框
        showUploadDialog() {
            this.uploadDialogVisible = true;
            this.uploadForm = { fileNames: [] };
            this.uploadFileList = [];
        },

        // 处理档案文件选择
        handleFileChange(file, fileList) {
            this.uploadFileList = fileList;
            // 为每个文件初始化名称和进度
            this.uploadForm.fileNames = fileList.map(f => f.name || ''); // 使用 f.name 作为默认值
            file.progress = 0;
            file.status = '';
        },

        // 处理档案文件移除
        handleFileRemove(file, fileList) {
            this.uploadFileList = fileList;
            // 更新文件名数组
            this.uploadForm.fileNames = this.uploadForm.fileNames.filter((_, index) => 
                index !== this.uploadFileList.findIndex(f => f.uid === file.uid)
            );
        },

        // 提交档案上传
        async submitUpload() {
            try {
                await this.$refs.uploadForm.validate();
                console.log('上传文件列表：', this.uploadFileList);
                if (this.uploadFileList.length === 0) {
                    this.$message.error('请选择要上传的文件');
                    return;
                }

                this.uploading = true;
                console.log('开始上传文件，文件列表：', this.uploadFileList);
                
                // 并行上传所有文件
                const uploadPromises = this.uploadFileList.map((file, index) => {
                    const formData = new FormData();
                    formData.append('file', file.raw);
                    formData.append('name', this.uploadForm.fileNames[index]);

                    console.log('准备上传文件：', {
                        fileName: file.name,
                        customName: this.uploadForm.fileNames[index],
                        fileSize: file.size,
                        fileType: file.type
                    });

                    return this.$http.post(`/asset/${this.currentStation.id}/archives`, formData, {
                        onUploadProgress: progressEvent => {
                            file.progress = Math.round((progressEvent.loaded * 90) / progressEvent.total);
                            file.status = 'warning';
                            console.log(`文件 ${file.name} 上传进度：${file.progress}%`);
                        }
                    }).then(response => {
                        console.log('文件上传响应：', response.data);
                        if (response.data.code === 200) {
                            file.progress = 100;
                            file.status = 'success';
                            console.log(`文件 ${file.name} 上传成功`);
                        } else {
                            file.status = 'exception';
                            console.error(`文件 ${file.name} 上传失败：`, response.data.message);
                            throw new Error(response.data.message);
                        }
                    }).catch(error => {
                        file.status = 'exception';
                        console.error(`文件 ${file.name} 上传出错：`, error);
                        throw error;
                    });
                });

                await Promise.all(uploadPromises);
                
                console.log('所有文件上传完成');
                this.$message.success('上传成功');
                this.uploadDialogVisible = false;
                this.uploadFileList = [];
                this.uploadForm.fileNames = [];
                this.fetchArchiveFiles();
            } catch (error) {
                console.error('上传过程发生错误:', error);
                this.$message.error('上传失败：' + (error.message || '未知错误'));
            } finally {
                this.uploading = false;
            }
        },

        // 格式化导入进度显示
        formatImportProgress(percentage) {
            if (percentage < 30) {
                return `上传中 ${percentage}%`;
            } else if (percentage < 100) {
                return `处理中 ${percentage}%`;
            } else {
                return `完成 ${percentage}%`;
            }
        },

        getFileIcon(type) {
            return type === 'image' ? 'el-icon-picture-outline' : 'el-icon-document';
        },

        formatFileSize(size) {
            if (size < 1024) {
                return size + ' B';
            } else if (size < 1024 * 1024) {
                return (size / 1024).toFixed(1) + ' KB';
            } else {
                return (size / (1024 * 1024)).toFixed(1) + ' MB';
            }
        },

        formatDate(date) {
            if (!date) return '';
            return new Date(date).toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit'
            });
        },

        handleDeleteStation(station) {
            this.$confirm('确认删除该电站？', '提示', {
                type: 'warning'
            }).then(() => {
                this.$http.delete(`/station/${station.id}`) // 确保删除电站的 API 路径正确
                    .then(response => {
                        if (response.data.code === 200) {
                            this.$message.success('电站删除成功');
                            this.fetchData();
                        } else {
                            this.$message.error(response.data.message);
                        }
                    })
                    .catch(error => {
                        console.error('删除电站失败:', error);
                        this.$message.error('删除电站失败');
                    });
            });
        },

        handleDeleteArchive(file) {
            this.$confirm('确认删除该档案文件？', '提示', {
                type: 'warning'
            }).then(() => {
                // 确保 stationId 和 archiveId 都正确传递
                const stationId = this.currentStation.id;
                const archiveId = file.id;
                
                this.$http.delete(`/asset/archive/${stationId}/${archiveId}`)
                    .then(response => {
                        if (response.data.code === 200) {
                            this.$message.success('文件删除成功');
                            this.fetchArchiveFiles();
                            this.selectedFile = null;
                        } else {
                            this.$message.error(response.data.message);
                        }
                    })
                    .catch(error => {
                        console.error('删除文件失败:', error);
                        this.$message.error('删除文件失败');
                    });
            });
        },

        // 监听档案对话框关闭
        handleArchiveDialogClose() {
            this.loading = false;
            this.currentStation = null;
            this.selectedFile = null;
            this.archiveFiles = [];
        },

        // 显示导入错误详情
        showImportErrorDetails(data) {
            // 构建失败记录的表格HTML
            let failedRecordsHtml = '';
            if (data.failed_records && data.failed_records.length > 0) {
                failedRecordsHtml = `
                    <div style="margin-top: 20px;">
                        <h4>失败记录详情（显示前20条）：</h4>
                        <table border="1" style="width: 100%; border-collapse: collapse; font-size: 12px;">
                            <thead>
                                <tr style="background-color: #f5f5f5;">
                                    <th style="padding: 5px;">行号</th>
                                    <th style="padding: 5px;">序号</th>
                                    <th style="padding: 5px;">姓名</th>
                                    <th style="padding: 5px;">国网户号</th>
                                    <th style="padding: 5px;">电话</th>
                                    <th style="padding: 5px;">安装地址</th>
                                    <th style="padding: 5px;">错误原因</th>
                                </tr>
                            </thead>
                            <tbody>`;

                data.failed_records.forEach(record => {
                    const rowData = record.data || {};
                    failedRecordsHtml += `
                        <tr>
                            <td style="padding: 5px;">${record.row_number}</td>
                            <td style="padding: 5px;">${rowData['序号'] || ''}</td>
                            <td style="padding: 5px;">${rowData['姓名'] || ''}</td>
                            <td style="padding: 5px;">${rowData['国网户号'] || ''}</td>
                            <td style="padding: 5px;">${rowData['电话'] || ''}</td>
                            <td style="padding: 5px;">${rowData['安装地址'] || ''}</td>
                            <td style="padding: 5px; color: red;">${this.simplifyErrorMessage(record.error)}</td>
                        </tr>`;
                });

                failedRecordsHtml += `
                            </tbody>
                        </table>
                    </div>`;
            }

            // 构建完整的错误信息
            let alertContent = `
                <div style="text-align: left;">
                    <h4>错误概要：</h4>
                    <ul>
                        ${data.errors.slice(0, 5).map(error => `<li>${error}</li>`).join('')}
                    </ul>
            `;

            // 添加修复建议
            if (data.suggestions && data.suggestions.length > 0) {
                alertContent += `
                    <h4>修复建议：</h4>
                    <ul>
                        ${data.suggestions.map(suggestion => `<li>${suggestion}</li>`).join('')}
                    </ul>
                `;
            }

            alertContent += failedRecordsHtml + '</div>';

            this.$confirm(alertContent, '导入结果详情', {
                confirmButtonText: '导出失败记录',
                cancelButtonText: '关闭',
                type: 'warning',
                dangerouslyUseHTMLString: true,
                customClass: 'import-error-dialog',
                distinguishCancelAndClose: true
            }).then(() => {
                // 用户点击"导出失败记录"
                this.exportFailedRecords(data.failed_records);
            }).catch(action => {
                // 用户点击"关闭"或点击X
                if (action === 'cancel') {
                    // 用户点击关闭按钮
                }
            });
        },

        // 简化错误信息
        simplifyErrorMessage(error) {
            if (error.includes('Incorrect decimal value')) {
                return '数值字段包含文本';
            } else if (error.includes('Incorrect integer value')) {
                return '整数字段包含文本';
            } else if (error.includes('Invalid datetime format')) {
                return '日期格式错误';
            } else if (error.includes('处理后数据为空')) {
                return '数据为空';
            } else {
                return '数据格式错误';
            }
        },

        // 导出失败记录为CSV文件
        exportFailedRecords(failedRecords) {
            if (!failedRecords || failedRecords.length === 0) {
                this.$message.warning('没有失败记录可导出');
                return;
            }

            // 构建CSV内容
            const headers = ['行号', '序号', '姓名', '国网户号', '电话', '安装地址', '错误原因'];
            let csvContent = headers.join(',') + '\n';

            failedRecords.forEach(record => {
                const rowData = record.data || {};
                const row = [
                    record.row_number,
                    `"${(rowData['序号'] || '').toString().replace(/"/g, '""')}"`,
                    `"${(rowData['姓名'] || '').toString().replace(/"/g, '""')}"`,
                    `"${(rowData['国网户号'] || '').toString().replace(/"/g, '""')}"`,
                    `"${(rowData['电话'] || '').toString().replace(/"/g, '""')}"`,
                    `"${(rowData['安装地址'] || '').toString().replace(/"/g, '""')}"`,
                    `"${this.simplifyErrorMessage(record.error).replace(/"/g, '""')}"`
                ];
                csvContent += row.join(',') + '\n';
            });

            // 创建下载链接
            const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `农户电站导入失败记录_${new Date().toISOString().slice(0, 10)}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            this.$message.success('失败记录已导出到CSV文件');
        },

        // 获取部门选项
        async fetchDepartments() {
            try {
                console.log('开始获取部门列表');
                const res = await this.$http.get(API.department.list);
                console.log('部门API返回完整数据:', res);
                console.log('部门数据项示例(第一条):', res.data.data[0]);
                
                if (res.data.code === 200) {
                    // 检查parent_id字段的具体形式(parentId或parent_id)
                    const firstDept = res.data.data[0];
                    const parentIdField = firstDept.hasOwnProperty('parentId') ? 'parentId' : 'parent_id';
                    console.log('部门使用的父ID字段名:', parentIdField);
                    
                    // 使用更灵活的筛选条件
                    this.departmentOptions = res.data.data.filter(dept => {
                        const parentId = dept[parentIdField];
                        console.log(`部门ID:${dept.id}, 名称:${dept.name}, 父ID:${parentId}`);
                        return (parentId == 18 || parentId == '18') && dept.id != 19;
                    });
                    
                    console.log('筛选后的部门列表数量:', this.departmentOptions.length);
                    console.log('筛选后的部门列表:', this.departmentOptions);
                    
                    // 如果没有找到符合条件的部门，显示所有部门
                    if (this.departmentOptions.length === 0) {
                        console.log('没有找到符合条件的部门，显示所有部门');
                        this.departmentOptions = res.data.data;
                    }
                }
            } catch (error) {
                console.error('获取部门列表失败:', error);
            }
        },
    }
};

// 注册组件
Vue.component('farmer-station-management', FarmerStationManagement);