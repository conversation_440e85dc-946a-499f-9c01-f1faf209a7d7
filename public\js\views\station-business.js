// 工商业电站组件
const BusinessStationManagement = {
    template: `
        <div class="station-management">
            <!-- 工具栏 -->
            <div class="toolbar" style="margin-bottom: 20px;">
                <el-form :inline="true" :model="searchForm">
                    <el-form-item>
                        <el-input v-model="searchForm.keyword" placeholder="单位名称/地址/户号" clearable></el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-input v-model="searchForm.department" placeholder="所属部门" clearable></el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="handleSearch">搜索</el-button>
                        <el-button @click="resetSearch">重置</el-button>
                        <el-button type="primary" @click="showCreateDialog">新增工商业电站</el-button>
                        <el-button type="primary" @click="handleImport">导入工商业电站数据</el-button>
                    </el-form-item>
                </el-form>
            </div>

            <!-- 电站列表 -->
            <el-table
                v-loading="loading"
                element-loading-text="加载中..."
                element-loading-spinner="el-icon-loading"
                element-loading-background="rgba(255, 255, 255, 0.8)"
                :data="tableData"
                border
                style="width: 100%">
                <el-table-column label="序号" width="80" align="center">
                    <template slot-scope="scope">
                        {{ (page - 1) * limit + scope.$index + 1 }}
                    </template>
                </el-table-column>
                <el-table-column prop="department" label="所属部门" min-width="120"></el-table-column>
                <el-table-column prop="company_name" label="单位名称" min-width="180"></el-table-column>
                <el-table-column prop="address" label="地址" min-width="200"></el-table-column>
                <el-table-column prop="cooperation_mode" label="合作模式" min-width="120"></el-table-column>
                <el-table-column prop="contact_method" label="对接方式" min-width="120"></el-table-column>
                <el-table-column prop="account_number" label="户号" min-width="150"></el-table-column>
                <el-table-column prop="capacity_kw" label="装机容量(kW)" width="140">
                    <template slot-scope="scope">
                        {{ scope.row.capacity_kw }} kW
                    </template>
                </el-table-column>
                <el-table-column prop="unit_price" label="单价(元/瓦)" width="120"></el-table-column>
                <el-table-column prop="investment_amount" label="投资额(万元)" width="130"></el-table-column>
                <el-table-column prop="panel_brand_power" label="组件品牌功率(W)" width="160"></el-table-column>
                <el-table-column prop="panel_count" label="组件块数" width="100"></el-table-column>
                <el-table-column prop="inverter_count" label="逆变器数量" width="110"></el-table-column>
                <el-table-column prop="grid_connection_time" label="并网时间" width="120">
                    <template slot-scope="scope">
                        {{ formatDate(scope.row.grid_connection_time, 'yyyy-MM-dd') }}
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="280" fixed="right">
                    <template slot-scope="scope">
                        <el-button size="small" type="primary" @click="showEditDialog(scope.row)">编辑</el-button>
                        <el-button size="small" type="success" @click="showArchiveDialog(scope.row)">档案</el-button>
                        <el-button size="small" type="danger" @click="handleDeleteStation(scope.row)">删除电站</el-button>
                    </template>
                </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="pagination" style="margin-top: 20px; text-align: right;">
                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page.sync="page"
                    :page-sizes="[10, 20, 50, 100]"
                    :page-size="limit"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total">
                </el-pagination>
            </div>

            <!-- 表单对话框 -->
            <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="700px" @close="resetFormDialog">
                <el-form :model="form" :rules="rules" ref="form" label-width="130px">
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="单位名称" prop="company_name">
                                <el-input v-model="form.company_name" placeholder="请输入单位名称"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="所属部门" prop="department">
                                <el-select v-model="form.department" placeholder="请选择所属部门" filterable clearable style="width: 100%;">
                                    <el-option
                                        v-for="item in departmentOptions"
                                        :key="item.id" 
                                        :label="item.name"
                                        :value="item.name">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="24">
                            <el-form-item label="地址" prop="address">
                                <el-input v-model="form.address" placeholder="请输入地址"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                     <el-row>
                        <el-col :span="12">
                           <el-form-item label="户号" prop="account_number">
                                <el-input v-model="form.account_number" placeholder="请输入户号"></el-input>
                           </el-form-item>
                        </el-col>
                         <el-col :span="12">
                            <el-form-item label="合作模式" prop="cooperation_mode">
                                <el-input v-model="form.cooperation_mode" placeholder="请输入合作模式"></el-input>
                           </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                         <el-col :span="12">
                            <el-form-item label="对接方式" prop="contact_method">
                                <el-input v-model="form.contact_method" placeholder="请输入对接方式"></el-input>
                           </el-form-item>
                         </el-col>
                         <el-col :span="12">
                             <el-form-item label="装机容量(kW)" prop="capacity_kw">
                                <el-input v-model="form.capacity_kw" placeholder="请输入装机容量(kW)"></el-input>
                           </el-form-item>
                         </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="单价(元/瓦)" prop="unit_price">
                                <el-input v-model="form.unit_price" placeholder="请输入单价(元/瓦)"></el-input>
                           </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="投资额(万元)" prop="investment_amount">
                                <el-input v-model="form.investment_amount" placeholder="请输入投资额(万元)"></el-input>
                           </el-form-item>
                        </el-col>
                     </el-row>
                     <el-row>
                        <el-col :span="12">
                            <el-form-item label="组件品牌功率(W)" prop="panel_brand_power">
                                <el-input v-model="form.panel_brand_power" placeholder="例如：天合 550W"></el-input>
                           </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="组件块数" prop="panel_count">
                                <el-input v-model="form.panel_count" placeholder="请输入组件块数"></el-input>
                           </el-form-item>
                        </el-col>
                     </el-row>
                     <el-row>
                        <el-col :span="12">
                            <el-form-item label="逆变器数量" prop="inverter_count">
                                <el-input v-model="form.inverter_count" placeholder="请输入逆变器数量"></el-input>
                           </el-form-item>
                        </el-col>
                         <el-col :span="12">
                             <el-form-item label="并网时间" prop="grid_connection_time">
                                <el-date-picker
                                    v-model="form.grid_connection_time"
                                    type="date"
                                    placeholder="选择并网时间"
                                    format="yyyy-MM-dd"
                                    value-format="yyyy-MM-dd"
                                    clearable>
                                </el-date-picker>
                           </el-form-item>
                         </el-col>
                     </el-row>
                </el-form>
                <div slot="footer" class="dialog-footer">
                    <el-button @click="dialogVisible = false">取 消</el-button>
                    <el-button type="primary" @click="handleSubmit">确 定</el-button>
                </div>
            </el-dialog>

            <!-- 导入对话框 -->
            <el-dialog title="导入工商业电站数据" :visible.sync="importDialogVisible" width="600px">
                <el-upload
                    ref="importUpload"
                    action=""
                    :auto-upload="false"
                    :on-change="handleImportFileChange"
                    :file-list="importFileList"
                    accept=".xlsx, .xls">
                    <el-button slot="trigger" type="primary">选取文件</el-button>
                    <el-button 
                        type="success" 
                        @click="submitImport" 
                        :loading="importing"
                        :disabled="importing">
                        {{ importing ? '正在导入' : '上传到服务器' }}
                    </el-button>
                    <div slot="tip" class="el-upload__tip">请上传xlsx/xls文件。模板列应包含：所属部门, 单位名称, 地址, 合作模式, 对接方式, 户号, 装机容量(MW), 单价(元/瓦), 投资额(万元), 组件品牌功率(W), 组件块数, 逆变器数量, 并网时间(yyyy-MM-dd)。</div>
                </el-upload>
                <el-progress 
                    v-if="importProgress > 0" 
                    :percentage="importProgress"
                    :status="importStatus">
                </el-progress>
            </el-dialog>

            <!-- 档案管理对话框 -->
            <el-dialog 
                title="档案管理" 
                :visible.sync="archiveDialogVisible" 
                fullscreen 
                :show-close="true"
                custom-class="archive-dialog"
                @closed="handleArchiveDialogClose">
                <div v-loading="loadingArchive"
                     element-loading-text="加载中..."
                     element-loading-spinner="el-icon-loading"
                     element-loading-background="rgba(255, 255, 255, 0.8)"
                     style="height: 100%">
                    <div class="archive-header">
                        <div class="header-left">
                            <span class="station-name">{{ currentStation ? currentStation.company_name : '' }}</span>
                            <el-tag size="small" type="info">共 {{ archiveFiles.length }} 个文件</el-tag>
                        </div>
                        <div class="header-right">
                            <el-button type="primary" size="small" icon="el-icon-upload2" @click="showUploadDialog">上传档案</el-button>
                        </div>
                    </div>
                    <el-container class="archive-container">
                        <el-aside width="280px" class="archive-left">
                            <el-menu
                                v-if="archiveFiles.length > 0"
                                :default-active="activeFileId"
                                @select="handleFileSelect">
                                <el-menu-item 
                                    v-for="file in archiveFiles" 
                                    :key="file.id"
                                    :index="file.id">
                                    <i :class="getFileIcon(file.type)"></i>
                                    <span slot="title" class="file-name">{{ file.name }}</span>
                                </el-menu-item>
                            </el-menu>
                            <div v-else class="no-files">
                                <i class="el-icon-folder-opened"></i>
                                <p>暂无档案文件</p>
                                <el-button type="text" @click="showUploadDialog">立即上传</el-button>
                            </div>
                        </el-aside>
                        <el-main class="archive-right">
                            <div v-if="!selectedFile" class="no-preview">
                                <i class="el-icon-document"></i>
                                <p>请选择要查看的档案</p>
                            </div>
                            <div v-else class="preview-container">
                                <div class="preview-header">
                                    <div class="file-title">
                                        <i :class="getFileIcon(selectedFile.type)"></i>
                                        <span>{{ selectedFile.name }}</span>
                                    </div>
                                    <div class="file-meta">
                                        <span>大小：{{ formatFileSize(selectedFile.file_size) }}</span>
                                        <span>上传时间：{{ formatDate(selectedFile.create_at, 'yyyy-MM-dd HH:mm') }}</span>
                                    </div>
                                    <el-button 
                                        type="danger" 
                                        size="small" 
                                        icon="el-icon-delete" 
                                        class="delete-button"
                                        @click="handleDeleteArchive(selectedFile)">删除文件</el-button>
                                </div>
                                <div class="preview-content">
                                    <div v-if="selectedFile.type === 'image'" class="image-preview">
                                    <img :src="selectedFile.url" alt="预览图片">
                                    </div>
                                    <div v-else-if="selectedFile.type === 'pdf'" class="pdf-preview">
                                        <iframe :src="selectedFile.url" style="width:100%; height: calc(100vh - 200px); border:none;"></iframe>
                                    </div>
                                    <div v-else class="no-preview-available">
                                        <p>此文件类型不支持在线预览。</p>
                                        <el-button type="primary" size="small" @click="downloadFile(selectedFile)">下载文件</el-button>
                                    </div>
                                </div>
                            </div>
                        </el-main>
                    </el-container>
                </div>
            </el-dialog>

            <!-- 档案上传对话框 -->
            <el-dialog title="上传档案" :visible.sync="uploadDialogVisible" width="500px">
                <el-form :model="uploadForm" :rules="uploadRules" ref="uploadForm">
                    <el-upload
                        ref="upload"
                        action=""
                        :auto-upload="false"
                        :on-change="handleFileChange"
                        :on-remove="handleFileRemove"
                        :file-list="uploadFileList"
                        multiple
                        accept=".jpg,.jpeg,.png,.pdf">
                        <el-button slot="trigger" type="primary">选择文件</el-button>
                    </el-upload>
                    <div v-for="(file, index) in uploadFileList" :key="file.uid" class="upload-item">
                        <el-form-item 
                            :label="file.name"
                            :prop="'fileNames.' + index"
                            :rules="{ required: true, message: '请输入文件名称', trigger: 'blur' }">
                            <el-input v-model="uploadForm.fileNames[index]" placeholder="请输入文件名称"></el-input>
                        </el-form-item>
                        <el-progress 
                            v-if="file.percentage > 0"
                            :percentage="file.percentage || 0"
                            :status="file.status">
                        </el-progress>
                    </div>
                </el-form>
                <div slot="footer">
                    <el-button @click="uploadDialogVisible = false">取 消</el-button>
                    <el-button type="primary" @click="submitUpload" :loading="uploading">确 定</el-button>
                </div>
            </el-dialog>
        </div>
    `,
    data() {
        return {
            searchForm: {
                keyword: '',
                department: '',
            },
            tableData: [],
            total: 0,
            page: 1,
            limit: 10,
            loading: false,
            dialogVisible: false,
            dialogTitle: '',
            form: {
                id: '',
                department: '',
                company_name: '',
                address: '',
                cooperation_mode: '',
                contact_method: '',
                account_number: '',
                capacity_kw: '',
                unit_price: '',
                investment_amount: '',
                panel_brand_power: '',
                panel_count: '',
                inverter_count: '',
                grid_connection_time: '',
            },
            rules: {
                department: [
                    { required: true, message: '请选择所属部门', trigger: 'change' }
                ],
                company_name: [
                    { required: true, message: '请输入单位名称', trigger: 'blur' }
                ],
                address: [
                    { required: true, message: '请输入地址', trigger: 'blur' }
                ],
                 cooperation_mode: [
                    { required: true, message: '请输入合作模式', trigger: 'blur' }
                ],
                 contact_method: [
                    { required: true, message: '请输入对接方式', trigger: 'blur' }
                ],
                account_number: [
                    { required: true, message: '请输入户号', trigger: 'blur' }
                ],
                capacity_kw: [
                    { required: true, message: '请输入装机容量(kW)', trigger: 'blur' },
                ],
                 unit_price: [
                ],
                 investment_amount: [
                ],
                 panel_brand_power: [
                     { required: true, message: '请输入组件品牌功率', trigger: 'blur' }
                 ],
                 panel_count: [
                ],
                 inverter_count: [
                ],
                 grid_connection_time: [
                     { required: true, message: '请选择并网时间', trigger: 'change' }
                 ]
            },
            departmentOptions: [],
            importDialogVisible: false,
            importFileList: [],
            importProgress: 0,
            importStatus: '',
            importing: false,
            archiveDialogVisible: false,
            archiveFiles: [],
            selectedFile: null,
            activeFileId: '',
            loadingArchive: false,
            uploadDialogVisible: false,
            uploadFileList: [],
            uploading: false,
            uploadForm: {
                fileNames: []
            },
            uploadRules: {
                fileNames: [{
                    validator: (rule, value, callback) => {
                         const index = parseInt(rule.field.split('.')[1]);
                        if (!this.uploadForm.fileNames[index]) {
                            callback(new Error('请输入文件名称'));
                        } else {
                            callback();
                        }
                    },
                    trigger: 'blur'
                }]
            },
            currentStation: null,
            uploadActionUrl: '',
            uploadHeaders: {},
            uploadData: {},
        }
    },
    created() {
        this.fetchDepartments();
        this.fetchData();
        this.uploadHeaders = {
            'Authorization': 'Bearer ' + localStorage.getItem('token')
        };
    },
    methods: {
        fetchDepartments() {
            this.$http.get('/department' /*, { params: { limit: 999, page: 1 } } */)
                .then(response => {
                    if (response.data.code === 200) {
                        let departments = [];
                        if (response.data.data && response.data.data.items) {
                            departments = response.data.data.items;
                        } else if (response.data.data && Array.isArray(response.data.data)) {
                            departments = response.data.data;
                        } else {
                             console.warn('无法从 /department 接口响应中提取部门列表:', response.data);
                        }
                        this.departmentOptions = departments.map(dept => ({ id: dept.id, name: dept.name }));
                        console.log('部门列表加载成功 (from /department):', this.departmentOptions);
                    } else {
                        this.$message.error(response.data.message || '获取部门列表失败');
                    }
                })
                .catch(error => {
                     console.error('获取部门列表失败:', error);
                     this.$message.error('获取部门列表失败，请检查后端接口或网络');
                });
        },
        fetchData() {
            this.loading = true;
            const params = {
                page: this.page,
                limit: this.limit,
                keyword: this.searchForm.keyword || '',
                department: this.searchForm.department || '',
            };
            
            console.log('请求工商业电站列表 - 参数:', params);
            
            this.$http.get('/business_station', { params })
                .then(response => {
                    console.log('工商业电站列表 API 返回:', response);
                    if (response.data.code === 200) {
                        this.tableData = response.data.data.items || [];
                        this.total = response.data.data.total || 0;
                        console.log('加载的工商业电站数量:', this.tableData.length);
                    } else {
                        this.$message.error(response.data.message || '获取工商业电站数据失败');
                    }
                })
                .catch(error => {
                    console.error('获取工商业电站数据失败:', error);
                    if (error.response && error.response.status === 404) {
                         this.$message.error('获取工商业电站列表失败：API接口未找到(/business_station)，请检查后端路由配置。');
                    } else {
                         this.$message.error('获取工商业电站数据失败，请检查网络或联系管理员');
                    }
                })
                .finally(() => {
                    this.loading = false;
                });
        },
        handleSearch() {
            this.page = 1;
            this.fetchData();
            console.log('执行工商业电站搜索，搜索条件:', this.searchForm);
        },
        resetSearch() {
            this.searchForm = {
                keyword: '',
                department: '',
            };
            this.page = 1;
            this.fetchData();
            console.log('重置工商业电站搜索条件');
        },
        showCreateDialog() {
                this.form = {
                    id: '',
                department: '',
                company_name: '',
                    address: '',
                cooperation_mode: '',
                contact_method: '',
                account_number: '',
                capacity_kw: '',
                unit_price: '',
                investment_amount: '',
                panel_brand_power: '',
                panel_count: '',
                inverter_count: '',
                grid_connection_time: '',
            };
                this.dialogVisible = true;
                this.dialogTitle = '新增工商业电站';
            this.$nextTick(() => {
                this.$refs.form.clearValidate();
            });
        },
        showEditDialog(row) {
                this.form = {
                    id: row.id,
                department: row.department,
                company_name: row.company_name,
                    address: row.address,
                cooperation_mode: row.cooperation_mode,
                contact_method: row.contact_method,
                account_number: row.account_number,
                capacity_kw: row.capacity_kw,
                unit_price: row.unit_price,
                investment_amount: row.investment_amount,
                panel_brand_power: row.panel_brand_power,
                panel_count: row.panel_count,
                inverter_count: row.inverter_count,
                grid_connection_time: row.grid_connection_time ? this.formatDate(row.grid_connection_time, 'yyyy-MM-dd') : null,
            };
            console.log('编辑工商业电站 - 表单数据:', this.form);
                
                this.dialogVisible = true;
                this.dialogTitle = '编辑工商业电站';
            this.$nextTick(() => {
                this.$refs.form.clearValidate();
            });
        },
        handleSubmit() {
            this.$refs.form.validate(valid => {
                if (valid) {
                    const isEdit = !!this.form.id;
                    const method = isEdit ? 'put' : 'post';
                    const url = isEdit ? `/business_station/${this.form.id}` : '/business_station';

                    const data = { ...this.form };
                    console.log('提交工商业电站数据:', data);
                    
                    this.$http[method](url, data)
                        .then(response => {
                            if (response.data.code === 200) {
                                this.$message.success(isEdit ? '工商业电站更新成功' : '工商业电站创建成功');
                                this.dialogVisible = false;
                                this.fetchData();
                            } else {
                                this.$message.error(response.data.message || (isEdit ? '更新失败' : '创建失败'));
                                console.error('工商业电站保存 API 错误:', response.data);
                            }
                        })
                        .catch(error => {
                            console.error('工商业电站保存操作失败:', error);
                            if (error.response && error.response.status === 404) {
                                this.$message.error('操作失败：API接口未找到，请检查后端路由配置。');
                            } else if (error.response && error.response.data && error.response.data.message) {
                                this.$message.error('操作失败：' + error.response.data.message);
                            }
                             else {
                                this.$message.error('操作失败，请检查网络或联系管理员');
                            }
                        });
                } else {
                     console.log('表单验证失败');
                     return false;
                }
            });
        },
        resetFormDialog() {
             if (this.$refs.form) {
                this.$refs.form.resetFields();
                this.$refs.form.clearValidate();
            }
             this.form = {
                id: '', department: '', company_name: '', address: '', cooperation_mode: '',
                contact_method: '', account_number: '', capacity_kw: '', unit_price: '',
                investment_amount: '', panel_brand_power: '', panel_count: '',
                inverter_count: '', grid_connection_time: ''
             };
        },
        handleImport() {
            this.importDialogVisible = true;
            this.importFileList = [];
            this.importProgress = 0;
            this.importStatus = '';
            if (this.$refs.importUpload) {
                this.$refs.importUpload.clearFiles();
            }
        },
        handleImportFileChange(file, fileList) {
            this.importFileList = fileList.slice(-1);
        },
        submitImport() {
            if (this.importFileList.length === 0) {
                this.$message.error('请先选择要导入的 Excel 文件');
                return;
            }

            const file = this.importFileList[0];
            const formData = new FormData();
            formData.append('file', file.raw);

            this.importProgress = 0;
            this.importStatus = '';
            this.importing = true;

            this.$http.post('/business_station/import', formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                    'Authorization': 'Bearer ' + localStorage.getItem('token')
                },
                onUploadProgress: progressEvent => {
                    const percent = Math.round((progressEvent.loaded * 90) / progressEvent.total);
                    this.importProgress = percent;
                    this.importStatus = percent === 100 ? 'success' : '';
                }
            }).then(response => {
                console.log('导入 API 返回:', response);
                if (response.data.code === 200) {
                    this.importProgress = 100;
                    this.importStatus = 'success';
                    this.$message.success(response.data.message || '导入成功');
                    this.fetchData();
                setTimeout(() => {
                    this.importDialogVisible = false;
                }, 1500);
                } else {
                    this.importStatus = 'exception';
                    this.$message.error(response.data.message || '导入失败');
                }
            }).catch(error => {
                this.importStatus = 'exception';
                console.error('导入失败:', error);
                 if (error.response && error.response.status === 404) {
                     this.$message.error('导入失败：API接口未找到(/business_station/import)，请检查后端路由配置。');
                 } else if (error.response && error.response.data && error.response.data.message) {
                     this.$message.error('导入失败：' + error.response.data.message);
                 } else {
                    this.$message.error('导入失败，请检查文件格式或联系管理员');
                 }
            }).finally(() => {
                this.importing = false;
            });
        },
        handleDeleteStation(station) {
            this.$confirm(`确认删除工商业电站 "${station.company_name}" 吗？`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.loading = true;
                this.$http.delete(`/business_station/${station.id}`)
                    .then(response => {
                        if (response.data.code === 200) {
                            this.$message.success('工商业电站删除成功');
                            this.fetchData();
                        } else {
                            this.$message.error(response.data.message || '删除失败');
                        }
                    })
                    .catch(error => {
                        console.error('删除工商业电站失败:', error);
                         if (error.response && error.response.status === 404) {
                             this.$message.error('删除失败：API接口未找到，请检查后端路由配置。');
                         } else if (error.response && error.response.data && error.response.data.message) {
                            this.$message.error('删除失败：' + error.response.data.message);
                        }else {
                            this.$message.error('删除失败，请检查网络或联系管理员');
                         }
                    })
                    .finally(() => {
                         this.loading = false;
                    });
            }).catch(() => {
                this.$message.info('已取消删除');
            });
        },
        showArchiveDialog(row) {
            if (!row || !row.id) {
                 console.error("无效的电站数据:", row);
                 this.$message.error("无法打开档案管理，电站信息不完整。");
                 return;
            }
            this.currentStation = row;
            this.archiveDialogVisible = true;
            this.selectedFile = null;
            this.activeFileId = '';
            this.archiveFiles = [];
            this.fetchArchiveFiles();
        },
        fetchArchiveFiles() {
            if (!this.currentStation || !this.currentStation.id) return Promise.reject("无有效电站ID");

            this.loadingArchive = true;
            const apiUrl = `/business_station/${this.currentStation.id}/archives`;
            console.log(`获取工商业档案列表 API: ${apiUrl}`);

            return this.$http.get(apiUrl, {
                headers: this.uploadHeaders
            })
                .then(response => {
                    console.log('工商业档案列表 API 返回:', response);
                    if (response.data.code === 200) {
                        this.archiveFiles = response.data.data.map(file => ({
                            ...file,
                            url: file.url.startsWith('http') ? file.url : (this.$http.defaults.baseURL || '') + file.url
                        }));
                         console.log('处理后的工商业档案文件列表:', this.archiveFiles);
                         if (this.archiveFiles.length > 0) {
                              this.handleFileSelect(this.archiveFiles[0].id);
                         }
                    } else {
                        this.$message.error(response.data.message || '获取工商业档案列表失败');
                    }
                })
                .catch(error => {
                    console.error('获取工商业档案列表失败:', error);
                    if (error.response && error.response.status === 404) {
                        this.$message.error('获取工商业档案列表失败：API接口未找到，请检查后端路由配置。');
                    } else {
                        this.$message.error('获取工商业档案列表失败');
                    }
                })
                .finally(() => {
                    this.loadingArchive = false;
                });
        },
        handleFileSelect(fileId) {
            this.activeFileId = fileId;
            this.selectedFile = this.archiveFiles.find(file => file.id === fileId);
            console.log('选中的文件:', this.selectedFile);
        },
        showUploadDialog() {
             if (!this.currentStation || !this.currentStation.id) {
                this.$message.error("请先选择一个电站");
                return;
            }
            this.uploadActionUrl = `/business_station/${this.currentStation.id}/archives`;
            this.uploadData = { name: '' };

            this.uploadForm = { fileNames: [] };
            this.uploadFileList = [];
             if (this.$refs.upload) {
                this.$refs.upload.clearFiles();
            }
            this.uploadDialogVisible = true;
        },
        handleFileRemove(file, fileList) {
            console.log('移除文件:', file, fileList);
            this.uploadFileList = fileList;
            const indexToRemove = this.uploadForm.fileNames.findIndex((name, idx) =>
                !fileList.some(f => f.uid === this.uploadFileList[idx]?.uid)
            );
            if (indexToRemove !== -1) {
                this.uploadForm.fileNames.splice(indexToRemove, 1);
            }
            this.regenerateFileNamesIndex();
        },
        handleFileChange(file, fileList) {
            console.log('文件状态改变:', file, fileList);
            this.uploadFileList = fileList;

            this.uploadForm.fileNames = fileList.map((f, index) => {
                 return this.uploadForm.fileNames[index] || f.name || '';
            });

            fileList.forEach(f => {
                if (f.percentage === undefined) this.$set(f, 'percentage', 0);
                if (f.status === undefined) this.$set(f, 'status', '');
            });

            this.regenerateFileNamesIndex();
        },
        regenerateFileNamesIndex() {
            const currentNames = [...this.uploadForm.fileNames];
            this.uploadForm.fileNames = this.uploadFileList.map((file, index) => {
                 return currentNames[index] !== undefined ? currentNames[index] : (file.name || '');
            });
            this.uploadFileList.forEach(f => {
                 if (f.percentage === undefined) this.$set(f, 'percentage', 0);
                 if (f.status === undefined) this.$set(f, 'status', '');
            });
            this.$nextTick(() => {
                 if(this.$refs.uploadForm) {
                     this.$refs.uploadForm.clearValidate();
                 }
            });
        },
        async submitUpload() {
            try {
                await this.$refs.uploadForm.validate();
                console.log('上传文件列表：', this.uploadFileList);
                if (this.uploadFileList.length === 0) {
                    this.$message.error('请选择要上传的文件');
                    return;
                }

                this.uploading = true;
                console.log('开始上传文件，文件列表：', this.uploadFileList);
                
                const uploadPromises = this.uploadFileList.map((file, index) => {
                    if (file.status === 'success' || file.status === 'uploading') {
                        console.log(`文件 ${file.name} 已成功或正在上传，跳过。`);
                        return Promise.resolve({ file, success: true });
                    }

                    const formData = new FormData();
                    formData.append('file', file.raw);
                    formData.append('name', this.uploadForm.fileNames[index]);

                    console.log('准备上传文件：', {
                        fileName: file.name,
                        customName: this.uploadForm.fileNames[index],
                        fileSize: file.size,
                        fileType: file.type
                    });

                    file.status = 'uploading';
                    file.percentage = 0;

                    return this.$http.post(this.uploadActionUrl, formData, {
                        headers: this.uploadHeaders,
                        onUploadProgress: progressEvent => {
                            const percent = Math.round((progressEvent.loaded * 100) / progressEvent.total);
                            file.percentage = percent;
                        }
                    }).then(response => {
                        console.log('文件上传响应：', response.data);
                        if (response.data.code === 200) {
                            file.status = 'success';
                            file.percentage = 100;
                            console.log(`文件 ${file.name} 上传成功`);
                            return { file, success: true };
                        } else {
                            file.status = 'fail';
                            console.error(`文件 ${file.name} 上传失败：`, response.data.message);
                            throw new Error(response.data.message || `文件 ${file.name} 上传失败`);
                        }
                    }).catch(error => {
                        file.status = 'fail';
                        console.error(`文件 ${file.name} 上传出错：`, error);
                        throw error;
                    });
                });

                await Promise.all(uploadPromises);
                
                console.log('所有文件上传尝试完成');
                const allSuccess = this.uploadFileList.every(f => f.status === 'success');
                if (allSuccess) {
                    this.$message.success('所有文件上传成功!');
                    this.uploadDialogVisible = false;
                    this.uploadFileList = [];
                    this.uploadForm.fileNames = [];
                    this.fetchArchiveFiles();
                } else {
                     this.$message.error('部分文件上传失败，请检查列表。');
                }
            } catch (error) {
                if (error instanceof Error) { 
                    console.error('上传过程发生错误:', error);
                    this.$message.error('上传失败：' + (error.message || '未知错误'));
                } else {
                    console.log('表单验证未通过', error);
                    this.$message.error('请为所有待上传文件输入有效的文件名称');
                }
            } finally {
                this.uploading = false;
            }
        },
        getFileIcon(type) {
            if (type === 'image') return 'el-icon-picture-outline';
            if (type === 'pdf') return 'el-icon-document';
            return 'el-icon-folder';
        },
        formatFileSize(size) {
            if (!size) return '0 B';
            const i = Math.floor(Math.log(size) / Math.log(1024));
            return (size / Math.pow(1024, i)).toFixed(1) + ' ' + ['B', 'KB', 'MB', 'GB', 'TB'][i];
        },
        formatDate(date, fmt = 'yyyy-MM-dd HH:mm:ss') {
            if (!date) return '';
            const d = new Date(date);
            if (isNaN(d.getTime())) return date;

            const o = {
                "M+": d.getMonth() + 1,
                "d+": d.getDate(),
                "H+": d.getHours(),
                "m+": d.getMinutes(),
                "s+": d.getSeconds(),
                "q+": Math.floor((d.getMonth() + 3) / 3),
                "S": d.getMilliseconds()
            };
            if (/(y+)/.test(fmt)) {
                fmt = fmt.replace(RegExp.$1, (d.getFullYear() + "").substr(4 - RegExp.$1.length));
            }
            for (let k in o) {
                if (new RegExp("(" + k + ")").test(fmt)) {
                    fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
                }
            }
            return fmt;
        },
        handleDeleteArchive(file) {
            this.$confirm(`确认删除档案文件 "${file.name}" 吗？`, '提示', {
                 confirmButtonText: '确定',
                 cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.loadingArchive = true;
                const archiveId = file.id;
                const apiUrl = `/businessStation/archives/${archiveId}`;
                
                this.$http.delete(apiUrl, {
                     headers: this.uploadHeaders
                })
                    .then(response => {
                        if (response.data.code === 200) {
                            this.$message.success('工商业档案文件删除成功');
                            this.fetchArchiveFiles();
                            if (this.selectedFile && this.selectedFile.id === archiveId) {
                                this.selectedFile = null;
                                this.activeFileId = '';
                            }
                        } else {
                            this.$message.error(response.data.message || '删除失败');
                        }
                    })
                    .catch(error => {
                        console.error('删除工商业档案文件失败:', error);
                        if (error.response && error.response.status === 404) {
                             this.$message.error('删除失败：API接口未找到，请检查后端路由配置。');
                         } else if (error.response && error.response.data && error.response.data.message) {
                            this.$message.error('删除失败：' + error.response.data.message);
                        } else {
                            this.$message.error('删除失败');
                        }
                    })
                    .finally(() => {
                        this.loadingArchive = false;
                    });
            }).catch(() => {
                 this.$message.info('已取消删除');
            });
        },
        downloadFile(file) {
            if (file && file.url) {
                 const link = document.createElement('a');
                 link.href = file.url;
                 link.download = file.original_name || file.name || 'download';
                 document.body.appendChild(link);
                 link.click();
                 document.body.removeChild(link);
            } else {
                 this.$message.error("文件链接无效，无法下载");
            }
        },
        handleArchiveDialogClose() {
            this.currentStation = null;
            this.archiveFiles = [];
            this.selectedFile = null;
            this.activeFileId = '';
            this.loadingArchive = false;
             console.log('档案管理对话框关闭');
        },
        handleSizeChange(val) {
            this.limit = val;
            this.page = 1;
            this.fetchData();
        },
        handleCurrentChange(val) {
            this.page = val;
            this.fetchData();
        },
    }
};

// 注册组件 (如果是在单独的文件中)
// Vue.component('business-station-management', BusinessStationManagement);