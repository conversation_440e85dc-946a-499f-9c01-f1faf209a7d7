<?php
declare (strict_types = 1);

namespace app\controller;

use think\facade\Db;
use think\Request;
use think\facade\Filesystem;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx;
use think\facade\Config;

class StationImportController
{
    // 批量处理大小
    private const BATCH_SIZE = 100;

    public function import(Request $request)
    {
        // 提高内存限制和执行时间
        ini_set('memory_limit', '512M');
        ini_set('max_execution_time', '300'); // 5分钟

        // 获取站点类型
        $type = $request->param('type', 1);  // 默认为农户电站类型

        // 获取上传的文件
        $file = $request->file('file');
        if (!$file) {
            return json(['code' => 400, 'message' => '文件上传失败']);
        }

        // 移动文件到临时目录
        $info = $file->move(runtime_path() . 'upload');
        if (!$info) {
            return json(['code' => 400, 'message' => '文件上传失败']);
        }

        // 获取文件路径
        $filePath = $info->getPathname();

        try {
            // 使用内存优化的方式读取Excel文件
            $reader = new Xlsx();
            $reader->setReadDataOnly(true); // 只读数据，不读格式
            $reader->setReadEmptyCells(false); // 不读空单元格

            $spreadsheet = $reader->load($filePath);
            $sheet = $spreadsheet->getActiveSheet();

            // 获取数据范围
            $highestRow = $sheet->getHighestRow();
            $highestColumn = $sheet->getHighestColumn();

            // 检查文件格式
            if ($highestRow < 4) {
                unlink($filePath);
                return json(['code' => 400, 'message' => '文件格式不正确，至少需要4行数据']);
            }

            // 获取列名（第3行，索引为2）
            $columns = [];
            $columnRange = 'A3:' . $highestColumn . '3';
            $headerRow = $sheet->rangeToArray($columnRange);

            if (isset($headerRow[0]) && is_array($headerRow[0])) {
                foreach ($headerRow[0] as $column) {
                    // 确保列名是字符串
                    if (is_object($column) && method_exists($column, '__toString')) {
                        $columns[] = (string)$column;
                    } elseif (is_scalar($column)) {
                        $columns[] = trim((string)$column);
                    }
                }
            }

            if (empty($columns)) {
                unlink($filePath);
                return json(['code' => 400, 'message' => '无法读取列标题，请检查Excel文件格式']);
            }

            // 获取映射关系
            $columnMapping = Config::get('import.station_column_mapping');

            // 预加载所有部门数据到内存中，避免重复查询
            $departments = [];
            $deptList = Db::name('department')->select();
            foreach ($deptList as $dept) {
                if (isset($dept['name']) && isset($dept['id'])) {
                    $departments[$dept['name']] = $dept['id'];
                }
            }

            $successCount = 0;
            $failCount = 0;
            $errors = [];

            // 分批处理数据
            for ($startRow = 4; $startRow <= $highestRow; $startRow += self::BATCH_SIZE) {
                $endRow = min($startRow + self::BATCH_SIZE - 1, $highestRow);
                $batchData = [];

                // 读取当前批次的数据
                $dataRange = 'A' . $startRow . ':' . $highestColumn . $endRow;
                $batchRows = $sheet->rangeToArray($dataRange);

                foreach ($batchRows as $rowIndex => $row) {
                    $actualRowNumber = $startRow + $rowIndex;

                    try {
                        // 确保 $row 是数组
                        if (!is_array($row)) {
                            $failCount++;
                            $errors[] = "第{$actualRowNumber}行: 数据格式错误";
                            continue;
                        }

                        $item = $this->processRow($row, $columns, $columnMapping, $departments, $type);
                        if (!empty($item)) {
                            $batchData[] = $item;
                        }
                    } catch (\Exception $e) {
                        $failCount++;
                        $errors[] = "第{$actualRowNumber}行: " . $e->getMessage();
                        continue;
                    } catch (\Error $e) {
                        $failCount++;
                        $errors[] = "第{$actualRowNumber}行: PHP错误 - " . $e->getMessage();
                        continue;
                    }
                }

                // 批量插入当前批次数据
                if (!empty($batchData)) {
                    try {
                        Db::name('station')->insertAll($batchData);
                        $successCount += count($batchData);
                    } catch (\Exception $e) {
                        $failCount += count($batchData);
                        $errors[] = "批次插入失败: " . $e->getMessage();
                    }
                }

                // 释放内存
                unset($batchData, $batchRows);

                // 强制垃圾回收
                if (function_exists('gc_collect_cycles')) {
                    gc_collect_cycles();
                }
            }

            // 释放资源
            $spreadsheet->disconnectWorksheets();
            unset($spreadsheet, $sheet);

        } catch (\Exception $e) {
            if (file_exists($filePath)) {
                unlink($filePath);
            }
            return json(['code' => 500, 'message' => '文件处理失败: ' . $e->getMessage()]);
        }

        // 删除临时文件
        if (file_exists($filePath)) {
            unlink($filePath);
        }

        // 返回结果
        $message = "导入完成！成功：{$successCount}条，失败：{$failCount}条";
        $result = [
            'code' => 200,
            'message' => $message,
            'data' => [
                'success' => $successCount,
                'fail' => $failCount,
                'errors' => array_slice($errors, 0, 10) // 最多返回10个错误
            ]
        ];

        return json($result);
    }

    /**
     * 处理单行数据
     */
    private function processRow($row, $columns, $columnMapping, $departments, $type)
    {
        $item = [];

        // 确保 $row 是数组且不为空
        if (!is_array($row) || empty($row)) {
            return $item;
        }

        foreach ($columns as $index => $column) {
            // 确保索引和列名都是有效的
            if (!is_string($column) || !is_int($index) || !isset($columnMapping[$column])) {
                continue;
            }

            // 确保行数据中存在对应索引
            if (!isset($row[$index])) {
                continue;
            }

            $fieldName = $columnMapping[$column];
            $value = $row[$index];

            // 处理值，确保是字符串
            if (is_object($value) && method_exists($value, '__toString')) {
                $value = (string)$value;
            } elseif (!is_scalar($value)) {
                continue; // 跳过非标量值
            }

            $value = trim((string)$value);

            // 跳过空值
            if ($value === '' || $value === null) {
                continue;
            }

            // 特殊字段处理
            if ($fieldName === 'construction_year') {
                // 项目建设年份处理：确保是4位年份
                $year = intval($value);
                if ($year >= 1900 && $year <= 2100) {
                    $item[$fieldName] = $year;
                }
            } elseif ($fieldName === 'department_id') {
                // 部室字段处理：如果是文本，需要查找对应的部门ID
                if (is_numeric($value)) {
                    $item[$fieldName] = intval($value);
                } else {
                    // 使用预加载的部门数据
                    if (is_array($departments) && isset($departments[$value])) {
                        $item[$fieldName] = $departments[$value];
                    }
                }
            } elseif (in_array($fieldName, ['capacity', 'component_power', 'fixed_income'])) {
                // 数值字段处理
                $numValue = floatval($value);
                if ($numValue > 0) {
                    $item[$fieldName] = $numValue;
                }
            } elseif (in_array($fieldName, ['component_count', 'arc_id'])) {
                // 整数字段处理
                $intValue = intval($value);
                if ($intValue > 0) {
                    $item[$fieldName] = $intValue;
                }
            } else {
                $item[$fieldName] = $value;
            }
        }

        // 设置必要的默认值
        $item['type'] = intval($type);
        if (!isset($item['status'])) {
            $item['status'] = 1; // 默认状态为正常
        }

        // 确保所有必需字段都有默认值，避免字段数量不匹配错误
        $defaultFields = [
            'total_serial' => '',
            'department_id' => 0,
            'original_department' => null,
            'construction_year' => null,
            'arc_id' => 0,
            'station_name' => '',
            'address' => null,
            'capacity' => null,
            'product_price' => null,
            'archive_status' => null,
            'bank_card_status' => null,
            'electricity_card' => null,
            'bank_name' => null,
            'bank_card' => null,
            'bank_branch' => null,
            'card_status' => null,
            'card_open_time' => null,
            'grid_contract_status' => null,
            'install_date' => null,
            'latitude' => null,
            'longitude' => null,
            'contact_name' => null,
            'contact_phone' => '',
            'business_type' => null,
            'contract_number' => null,
            'gongwang_account' => null,
            'id_card' => null,
            'county' => null,
            'town' => null,
            'village' => null,
            'component_brand' => null,
            'component_count' => null,
            'component_power' => null,
            'inverter_brand' => null,
            'inverter_serial' => null,
            'fixed_income' => null,
            'abnormal_remark' => null,
            'is_deleted' => 0
        ];

        // 为缺失的字段设置默认值
        foreach ($defaultFields as $field => $defaultValue) {
            if (!isset($item[$field])) {
                $item[$field] = $defaultValue;
            }
        }

        // 设置station_name为contact_name的值（如果contact_name存在）
        if (!empty($item['contact_name']) && empty($item['station_name'])) {
            $item['station_name'] = $item['contact_name'];
        }

        // 设置时间戳
        $now = date('Y-m-d H:i:s');
        $item['create_at'] = $now;
        $item['update_at'] = $now;

        return $item;
    }
}