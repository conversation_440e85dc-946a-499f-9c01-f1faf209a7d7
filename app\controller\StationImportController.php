<?php
declare (strict_types = 1);

namespace app\controller;

use think\facade\Db;
use think\Request;
use think\facade\Filesystem;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx;
use think\facade\Config;

class StationImportController
{
    // 批量处理大小 - 针对大数据量优化
    private const BATCH_SIZE = 50;

    public function import(Request $request)
    {
        // 提高内存限制和执行时间 - 针对15000+行数据优化
        ini_set('memory_limit', '1024M'); // 增加到1GB
        ini_set('max_execution_time', '600'); // 增加到10分钟

        // 获取站点类型
        $type = $request->param('type', 1);  // 默认为农户电站类型

        // 获取上传的文件
        $file = $request->file('file');
        if (!$file) {
            return json(['code' => 400, 'message' => '文件上传失败']);
        }

        // 移动文件到临时目录
        $info = $file->move(runtime_path() . 'upload');
        if (!$info) {
            return json(['code' => 400, 'message' => '文件上传失败']);
        }

        // 获取文件路径
        $filePath = $info->getPathname();

        try {
            // 使用内存优化的方式读取Excel文件
            $reader = new Xlsx();
            $reader->setReadDataOnly(true); // 只读数据，不读格式
            $reader->setReadEmptyCells(false); // 不读空单元格

            $spreadsheet = $reader->load($filePath);
            $sheet = $spreadsheet->getActiveSheet();

            // 获取数据范围
            $highestRow = $sheet->getHighestRow();
            $highestColumn = $sheet->getHighestColumn();

            // 检查文件格式
            if ($highestRow < 4) {
                unlink($filePath);
                return json(['code' => 400, 'message' => '文件格式不正确，至少需要4行数据']);
            }

            // 获取列名（第3行，索引为2）
            $columns = [];
            $columnRange = 'A3:' . $highestColumn . '3';
            $headerRow = $sheet->rangeToArray($columnRange);

            if (isset($headerRow[0]) && is_array($headerRow[0])) {
                foreach ($headerRow[0] as $column) {
                    // 确保列名是字符串
                    if (is_object($column) && method_exists($column, '__toString')) {
                        $columns[] = (string)$column;
                    } elseif (is_scalar($column)) {
                        $columns[] = trim((string)$column);
                    }
                }
            }

            if (empty($columns)) {
                unlink($filePath);
                return json(['code' => 400, 'message' => '无法读取列标题，请检查Excel文件格式']);
            }

            // 记录列标题信息
            error_log('Excel列标题: ' . json_encode($columns, JSON_UNESCAPED_UNICODE));

            // 获取映射关系
            $columnMapping = Config::get('import.station_column_mapping');

            // 验证列标题映射
            $mappedFields = [];
            foreach ($columns as $index => $column) {
                if (!empty($column) && isset($columnMapping[$column])) {
                    $mappedFields[$index] = $columnMapping[$column];
                }
            }

            error_log('成功映射的字段: ' . json_encode($mappedFields, JSON_UNESCAPED_UNICODE));

            // 如果映射的字段太少，可能是列标题不匹配
            if (count($mappedFields) < 5) {
                unlink($filePath);
                return json([
                    'code' => 400,
                    'message' => '列标题映射失败，请检查Excel文件的列标题是否与模板一致。成功映射字段数: ' . count($mappedFields) . '，需要至少5个字段匹配。'
                ]);
            }

            // 预加载所有部门数据到内存中，避免重复查询
            $departments = [];
            $deptList = Db::name('department')->select();
            foreach ($deptList as $dept) {
                if (isset($dept['name']) && isset($dept['id'])) {
                    $departments[$dept['name']] = $dept['id'];
                }
            }

            $successCount = 0;
            $failCount = 0;
            $errors = [];
            $failedRecords = []; // 存储失败的记录详情
            $totalRows = $highestRow - 3; // 总数据行数（排除前3行标题）
            $processedRows = 0;

            // 记录开始导入
            error_log("开始导入农户电站数据，总行数: {$totalRows}");

            // 分批处理数据
            for ($startRow = 4; $startRow <= $highestRow; $startRow += self::BATCH_SIZE) {
                $endRow = min($startRow + self::BATCH_SIZE - 1, $highestRow);
                $batchData = [];

                // 读取当前批次的数据
                $dataRange = 'A' . $startRow . ':' . $highestColumn . $endRow;
                $batchRows = $sheet->rangeToArray($dataRange);

                foreach ($batchRows as $rowIndex => $row) {
                    $actualRowNumber = $startRow + $rowIndex;

                    try {
                        // 确保 $row 是数组
                        if (!is_array($row)) {
                            $failCount++;
                            $errors[] = "第{$actualRowNumber}行: 数据格式错误";
                            continue;
                        }

                        // 检查行是否为空
                        $hasData = false;
                        foreach ($row as $cell) {
                            if (!empty(trim((string)$cell))) {
                                $hasData = true;
                                break;
                            }
                        }

                        if (!$hasData) {
                            // 跳过空行，不计入失败数
                            continue;
                        }

                        $item = $this->processRow($row, $columns, $columnMapping, $departments, $type, $actualRowNumber);
                        if (!empty($item)) {
                            // 逐条插入，而不是批量插入
                            try {
                                Db::name('station')->insert($item);
                                $successCount++;
                                $processedRows++;

                                // 每100条记录一次进度
                                if ($processedRows % 100 === 0) {
                                    $progress = round(($processedRows / $totalRows) * 100, 2);
                                    error_log("导入进度: {$progress}% ({$processedRows}/{$totalRows})");
                                }

                            } catch (\Exception $e) {
                                $failCount++;
                                $processedRows++;
                                $errorMsg = "第{$actualRowNumber}行插入失败: " . $e->getMessage();
                                $errors[] = $errorMsg;

                                // 收集失败记录的详细信息
                                $failedRecord = [
                                    'row_number' => $actualRowNumber,
                                    'error' => $e->getMessage(),
                                    'data' => $this->extractKeyFields($row, $columns, $columnMapping)
                                ];
                                $failedRecords[] = $failedRecord;

                                error_log($errorMsg);
                            }
                        } else {
                            $failCount++;
                            $processedRows++;
                            $errorMsg = "第{$actualRowNumber}行: 处理后数据为空";
                            $errors[] = $errorMsg;

                            // 收集失败记录的详细信息
                            $failedRecord = [
                                'row_number' => $actualRowNumber,
                                'error' => '处理后数据为空',
                                'data' => $this->extractKeyFields($row, $columns, $columnMapping)
                            ];
                            $failedRecords[] = $failedRecord;
                        }
                    } catch (\Exception $e) {
                        $failCount++;
                        $errors[] = "第{$actualRowNumber}行: " . $e->getMessage();
                        continue;
                    } catch (\Error $e) {
                        $failCount++;
                        $errors[] = "第{$actualRowNumber}行: PHP错误 - " . $e->getMessage();
                        continue;
                    }
                }

                // 释放内存
                unset($batchRows);

                // 强制垃圾回收
                if (function_exists('gc_collect_cycles')) {
                    gc_collect_cycles();
                }
            }

            // 释放资源
            $spreadsheet->disconnectWorksheets();
            unset($spreadsheet, $sheet);

        } catch (\Exception $e) {
            if (file_exists($filePath)) {
                unlink($filePath);
            }
            return json(['code' => 500, 'message' => '文件处理失败: ' . $e->getMessage()]);
        }

        // 删除临时文件
        if (file_exists($filePath)) {
            unlink($filePath);
        }

        // 分析错误类型并提供建议
        $suggestions = $this->analyzeErrors($errors);

        // 返回结果
        $message = "导入完成！成功：{$successCount}条，失败：{$failCount}条";
        $result = [
            'code' => 200,
            'message' => $message,
            'data' => [
                'success' => $successCount,
                'fail' => $failCount,
                'errors' => array_slice($errors, 0, 10), // 最多返回10个错误
                'suggestions' => $suggestions, // 添加修复建议
                'failed_records' => array_slice($failedRecords, 0, 20) // 最多返回20条失败记录详情
            ]
        ];

        return json($result);
    }

    /**
     * 处理单行数据
     */
    private function processRow($row, $columns, $columnMapping, $departments, $type, $rowNumber = 0)
    {
        $item = [];

        // 确保 $row 是数组且不为空
        if (!is_array($row) || empty($row)) {
            return $item;
        }

        foreach ($columns as $index => $column) {
            // 确保索引和列名都是有效的
            if (!is_string($column) || !is_int($index) || !isset($columnMapping[$column])) {
                continue;
            }

            // 确保行数据中存在对应索引
            if (!isset($row[$index])) {
                continue;
            }

            $fieldName = $columnMapping[$column];
            $value = $row[$index];

            // 处理值，确保是字符串
            if (is_object($value) && method_exists($value, '__toString')) {
                $value = (string)$value;
            } elseif (!is_scalar($value)) {
                continue; // 跳过非标量值
            }

            $value = trim((string)$value);

            // 跳过空值
            if ($value === '' || $value === null) {
                continue;
            }

            // 特殊字段处理
            if ($fieldName === 'construction_year') {
                // 项目建设年份处理：确保是4位年份
                $year = intval($value);
                if ($year >= 1900 && $year <= 2100) {
                    $item[$fieldName] = $year;
                }
            } elseif ($fieldName === 'department_id') {
                // 部室字段处理：如果是文本，需要查找对应的部门ID
                if (is_numeric($value)) {
                    $item[$fieldName] = intval($value);
                } else {
                    // 使用预加载的部门数据
                    if (is_array($departments) && isset($departments[$value])) {
                        $item[$fieldName] = $departments[$value];
                    }
                }
            } elseif (in_array($fieldName, ['capacity', 'component_power', 'fixed_income'])) {
                // 数值字段处理 - 严格验证数据类型
                $cleanValue = str_replace(',', '', trim($value)); // 移除千分位逗号和空格

                // 检查是否包含非数字字符（除了小数点）
                if (preg_match('/^[0-9]*\.?[0-9]+$/', $cleanValue) || $cleanValue === '0' || $cleanValue === '') {
                    $numValue = floatval($cleanValue);
                    if ($numValue >= 0) { // 允许0值
                        $item[$fieldName] = $numValue;
                    }
                } else {
                    // 如果包含文字，说明列位置可能错乱，跳过这个值
                    error_log("第{$rowNumber}行 数值字段 {$fieldName} 包含非数字内容: '{$value}', 列位置: {$index}, 跳过此值");
                }
            } elseif (in_array($fieldName, ['component_count', 'arc_id'])) {
                // 整数字段处理 - 严格验证数据类型
                $cleanValue = str_replace(',', '', trim($value)); // 移除千分位逗号和空格

                // 检查是否为纯数字
                if (preg_match('/^[0-9]+$/', $cleanValue) || $cleanValue === '0' || $cleanValue === '') {
                    $intValue = intval($cleanValue);
                    if ($intValue >= 0) { // 允许0值
                        $item[$fieldName] = $intValue;
                    }
                } else {
                    // 如果包含文字，说明列位置可能错乱，跳过这个值
                    error_log("第{$rowNumber}行 整数字段 {$fieldName} 包含非数字内容: '{$value}', 列位置: {$index}, 跳过此值");
                }
            } elseif ($fieldName === 'contact_phone') {
                // 电话号码处理 - 清理格式
                $phone = preg_replace('/[^\d]/', '', $value); // 只保留数字
                if (strlen($phone) >= 7) { // 至少7位数字
                    $item[$fieldName] = $phone;
                }
            } elseif ($fieldName === 'id_card') {
                // 身份证号处理 - 清理格式
                $idCard = preg_replace('/[^\dXx]/', '', $value); // 只保留数字和X
                if (strlen($idCard) === 15 || strlen($idCard) === 18) {
                    $item[$fieldName] = strtoupper($idCard);
                }
            } else {
                // 其他字段 - 清理空白字符
                $cleanValue = trim($value);
                if ($cleanValue !== '') {
                    $item[$fieldName] = $cleanValue;
                }
            }
        }

        // 设置必要的默认值
        $item['type'] = intval($type);
        if (!isset($item['status'])) {
            $item['status'] = 1; // 默认状态为正常
        }

        // 确保所有必需字段都有默认值，避免字段数量不匹配错误
        $defaultFields = [
            'total_serial' => '',
            'department_id' => 0,
            'original_department' => null,
            'construction_year' => null,
            'arc_id' => 0,
            'station_name' => '',
            'address' => null,
            'capacity' => null,
            'product_price' => null,
            'archive_status' => null,
            'bank_card_status' => null,
            'electricity_card' => null,
            'bank_name' => null,
            'bank_card' => null,
            'bank_branch' => null,
            'card_status' => null,
            'card_open_time' => null,
            'grid_contract_status' => null,
            'install_date' => null,
            'latitude' => null,
            'longitude' => null,
            'contact_name' => null,
            'contact_phone' => '',
            'business_type' => null,
            'contract_number' => null,
            'gongwang_account' => null,
            'id_card' => null,
            'county' => null,
            'town' => null,
            'village' => null,
            'component_brand' => null,
            'component_count' => null,
            'component_power' => null,
            'inverter_brand' => null,
            'inverter_serial' => null,
            'fixed_income' => null,
            'abnormal_remark' => null,
            'is_deleted' => 0
        ];

        // 为缺失的字段设置默认值
        foreach ($defaultFields as $field => $defaultValue) {
            if (!isset($item[$field])) {
                $item[$field] = $defaultValue;
            }
        }

        // 设置station_name为contact_name的值（如果contact_name存在）
        if (!empty($item['contact_name']) && empty($item['station_name'])) {
            $item['station_name'] = $item['contact_name'];
        }

        // 设置时间戳
        $now = date('Y-m-d H:i:s');
        $item['create_at'] = $now;
        $item['update_at'] = $now;

        return $item;
    }

    /**
     * 分析错误类型并提供修复建议
     */
    private function analyzeErrors($errors)
    {
        $suggestions = [];
        $errorPatterns = [
            'decimal' => 0,
            'integer' => 0,
            'datetime' => 0,
            'column_mismatch' => 0
        ];

        foreach ($errors as $error) {
            if (strpos($error, 'Incorrect decimal value') !== false) {
                $errorPatterns['decimal']++;
            } elseif (strpos($error, 'Incorrect integer value') !== false) {
                $errorPatterns['integer']++;
            } elseif (strpos($error, 'Invalid datetime format') !== false) {
                $errorPatterns['datetime']++;
            } elseif (strpos($error, 'Column count doesn\'t match') !== false) {
                $errorPatterns['column_mismatch']++;
            }
        }

        if ($errorPatterns['decimal'] > 0 || $errorPatterns['integer'] > 0) {
            $suggestions[] = "检测到数据类型错误：数字字段包含文本内容。请检查Excel文件的列顺序是否正确，确保数字字段（如电站容量、组件数量）只包含数字。";
        }

        if ($errorPatterns['column_mismatch'] > 0) {
            $suggestions[] = "检测到列数量不匹配错误。请确保Excel文件的列标题与模板完全一致，删除多余的空列。";
        }

        if (count($suggestions) === 0 && count($errors) > 0) {
            $suggestions[] = "建议：1) 检查Excel文件第3行的列标题是否与标准模板一致；2) 确保数据在正确的列中；3) 先导入少量数据测试。";
        }

        return $suggestions;
    }

    /**
     * 提取失败记录的关键字段信息
     */
    private function extractKeyFields($row, $columns, $columnMapping)
    {
        $keyFields = ['序号', '姓名', '国网户号', '电话', '安装地址'];
        $extractedData = [];

        foreach ($columns as $index => $column) {
            if (in_array($column, $keyFields) && isset($row[$index])) {
                $value = $row[$index];
                if (is_object($value) && method_exists($value, '__toString')) {
                    $value = (string)$value;
                } elseif (!is_scalar($value)) {
                    $value = '无法读取';
                }
                $extractedData[$column] = trim((string)$value);
            }
        }

        return $extractedData;
    }
}