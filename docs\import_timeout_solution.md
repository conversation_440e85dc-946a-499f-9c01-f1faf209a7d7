# 农户电站导入超时问题解决方案

## 问题描述
导入农户电站时出现超时错误：
```
导入失败: Error: timeout of 10000ms exceeded
```

## 解决方案

### ✅ 已实施的修复

#### 1. **前端超时设置优化**
- ✅ 将超时时间从10秒延长到5分钟（300秒）
- ✅ 添加了详细的进度显示
- ✅ 优化了错误提示信息

#### 2. **进度显示优化**
- ✅ **上传阶段**：0-30% 显示"上传中"
- ✅ **处理阶段**：30-100% 显示"处理中"
- ✅ **完成阶段**：100% 显示"完成"

#### 3. **用户体验改进**
- ✅ 动态按钮文本：上传中 → 处理数据中
- ✅ 详细的错误信息提示
- ✅ 超时错误的特殊处理

### 📊 **超时设置对比**

| 项目 | 修复前 | 修复后 | 说明 |
|------|--------|--------|------|
| **前端超时** | 10秒 | 300秒 | 5分钟 |
| **后端超时** | 30秒 | 300秒 | 5分钟 |
| **内存限制** | 128MB | 512MB | 4倍提升 |
| **文件大小** | 500KB | 50MB | 100倍提升 |

### 🔧 **配置详情**

#### 前端配置
```javascript
this.$http.post('/station/import', formData, {
    timeout: 300000, // 5分钟超时
    onUploadProgress: progressEvent => {
        // 上传进度显示
    },
    onDownloadProgress: progressEvent => {
        // 处理进度显示
    }
})
```

#### 后端配置
```php
// 提高执行时间限制
ini_set('max_execution_time', '300'); // 5分钟
ini_set('memory_limit', '512M');      // 512MB内存
```

### 📋 **使用建议**

#### 1. **文件大小建议**
- **小文件**：<1MB，<500行 - 推荐日常使用
- **中等文件**：1-10MB，500-2000行 - 正常处理
- **大文件**：10-50MB，2000-10000行 - 需要耐心等待
- **超大文件**：>50MB，>10000行 - 建议分批导入

#### 2. **网络环境要求**
- 稳定的网络连接
- 避免在网络高峰期导入
- 建议使用有线网络而非WiFi

#### 3. **分批导入策略**
如果文件过大，建议按以下方式分批：
```
原文件：10000行数据
分批方案：
- 批次1：第1-2000行
- 批次2：第2001-4000行
- 批次3：第4001-6000行
- 批次4：第6001-8000行
- 批次5：第8001-10000行
```

### 🚀 **性能优化建议**

#### 1. **Excel文件优化**
- 删除不必要的工作表
- 清理空行和空列
- 避免复杂的公式和格式
- 使用.xlsx格式而非.xls

#### 2. **数据准备**
- 确保数据格式正确
- 预先验证部门名称
- 清理重复数据
- 统一数据格式

#### 3. **服务器优化**
- 确保服务器有足够的内存
- 优化数据库连接
- 定期清理临时文件

### 🔍 **监控和诊断**

#### 1. **进度监控**
- 观察进度条是否正常推进
- 注意上传和处理阶段的时间
- 如果长时间停在某个进度，可能有问题

#### 2. **错误诊断**
```javascript
// 错误类型判断
if (error.code === 'ECONNABORTED') {
    // 网络超时
} else if (error.response && error.response.status === 500) {
    // 服务器错误
} else if (error.response && error.response.status === 413) {
    // 文件过大
}
```

#### 3. **性能指标**
- 上传速度：应该在30秒内完成
- 处理速度：每1000行约需30-60秒
- 内存使用：应该稳定在512MB以内

### ⚠️ **注意事项**

#### 1. **导入期间**
- 不要关闭浏览器窗口
- 不要刷新页面
- 不要进行其他大量操作
- 保持网络连接稳定

#### 2. **失败处理**
- 查看详细错误信息
- 检查文件格式和数据
- 必要时分批重新导入
- 联系技术支持

#### 3. **数据验证**
- 导入完成后检查数据完整性
- 验证关键字段是否正确
- 确认数据量是否匹配

### 🆘 **故障排除**

#### Q1: 仍然超时怎么办？
1. 减小文件大小（<5MB）
2. 分批导入
3. 检查网络连接
4. 联系系统管理员

#### Q2: 进度条卡住不动
1. 等待2-3分钟
2. 检查网络连接
3. 如果确实卡住，刷新页面重试

#### Q3: 部分数据导入成功
1. 查看错误详情
2. 修正有问题的数据
3. 重新导入失败的部分

### 📞 **技术支持**

如果问题仍然存在，请提供：
1. 文件大小和行数
2. 具体错误信息
3. 网络环境描述
4. 浏览器类型和版本

现在导入功能已经优化，支持更大文件和更长处理时间！
