# 农户电站导入测试模板

## Excel文件格式示例

### 文件结构
```
第1行：标题行（可选）
第2行：空行（可选）
第3行：列标题行（必须）
第4行开始：数据行
```

### 列标题示例（第3行）
```
序号 | 部室 | 原部室 | 项目建设年份 | 部室档案序号 | 姓名 | 国网户号 | 身份证号 | 电话 | 县区 | 乡镇 | 安装地址 | 组件品牌 | 组件数量 | 组件功率 | 电站容量 | 逆变器品牌 | 逆变器序列号 | 固定收益 | 异常电站备注
```

### 数据示例（第4行开始）
```
001 | 技术部 | 原技术部 | 2023 | 001 | 张三 | 12345678901 | 123456789012345678 | 13800138000 | 某县 | 某镇 | 某村某组 | 晶科 | 20 | 540 | 10.8 | 华为 | SN123456 | 8000 | 正常运行
002 | 运维部 | 原运维部 | 2022 | 002 | 李四 | 12345678902 | 123456789012345679 | 13800138001 | 某县 | 某镇 | 某村某组 | 隆基 | 18 | 550 | 9.9 | 阳光 | SN123457 | 7500 | 
```

## 字段说明

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| 序号 | 文本 | 是 | 电站唯一标识 |
| 部室 | 文本/数字 | 是 | 部门名称或部门ID |
| 原部室 | 文本 | 否 | 原所属部门 |
| 项目建设年份 | 数字 | 否 | 1900-2100年 |
| 部室档案序号 | 数字 | 否 | 档案编号 |
| 姓名 | 文本 | 是 | 联系人姓名 |
| 国网户号 | 文本 | 否 | 国家电网户号 |
| 身份证号 | 文本 | 否 | 18位身份证号 |
| 电话 | 文本 | 否 | 联系电话 |
| 县区 | 文本 | 否 | 所在县区 |
| 乡镇 | 文本 | 否 | 所在乡镇 |
| 安装地址 | 文本 | 是 | 电站安装地址 |
| 组件品牌 | 文本 | 否 | 太阳能组件品牌 |
| 组件数量 | 数字 | 否 | 组件数量 |
| 组件功率 | 数字 | 否 | 单个组件功率(W) |
| 电站容量 | 数字 | 否 | 总装机容量(kW) |
| 逆变器品牌 | 文本 | 否 | 逆变器品牌 |
| 逆变器序列号 | 文本 | 否 | 逆变器序列号 |
| 固定收益 | 数字 | 否 | 固定收益金额 |
| 异常电站备注 | 文本 | 否 | 异常情况说明 |

## 注意事项

### 1. 文件格式
- 支持 .xlsx 和 .xls 格式
- 建议使用 .xlsx 格式
- 文件大小建议不超过50MB

### 2. 数据要求
- 列标题必须在第3行
- 数据从第4行开始
- 空值字段可以留空
- 部门名称必须与系统中的部门名称一致

### 3. 性能建议
- 单次导入建议不超过5000行
- 大文件建议分批导入
- 确保网络连接稳定

### 4. 错误处理
- 系统会显示详细的导入结果
- 失败的行会显示具体错误原因
- 可以根据错误信息修正数据后重新导入

## 测试步骤

1. **准备测试文件**
   - 创建包含上述格式的Excel文件
   - 包含2-10行测试数据

2. **执行导入**
   - 进入农户电站管理页面
   - 点击"导入农户电站数据"按钮
   - 选择测试文件并上传

3. **验证结果**
   - 查看导入成功/失败统计
   - 检查导入的数据是否正确
   - 验证新增字段是否正常显示

4. **性能测试**
   - 测试不同大小的文件
   - 验证内存使用是否稳定
   - 确认大文件导入不会超时

## 常见问题

### Q: 导入时提示"部门名称不存在"
A: 确保Excel中的部门名称与系统中的部门名称完全一致

### Q: 导入时提示"年份格式错误"
A: 项目建设年份必须是1900-2100之间的4位数字

### Q: 导入速度很慢
A: 建议将大文件分割成多个小文件分批导入

### Q: 部分数据导入失败
A: 查看错误详情，根据提示修正数据格式后重新导入
