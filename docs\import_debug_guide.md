# 农户电站导入调试指南

## 错误："Illegal offset type" 解决方案

### 问题原因
"Illegal offset type" 错误通常发生在以下情况：
1. 使用非字符串/整数作为数组索引
2. Excel单元格包含对象而不是标量值
3. 数组结构不符合预期

### 已实施的修复措施

#### 1. **数组索引类型检查**
```php
// 确保索引和列名都是有效的
if (!is_string($column) || !is_int($index) || !isset($columnMapping[$column])) {
    continue;
}
```

#### 2. **Excel值类型处理**
```php
// 处理Excel对象值
if (is_object($value) && method_exists($value, '__toString')) {
    $value = (string)$value;
} elseif (!is_scalar($value)) {
    continue; // 跳过非标量值
}
```

#### 3. **部门数据预加载优化**
```php
// 使用 select() 替代 column() 避免索引问题
$deptList = Db::name('department')->select();
foreach ($deptList as $dept) {
    if (isset($dept['name']) && isset($dept['id'])) {
        $departments[$dept['name']] = $dept['id'];
    }
}
```

#### 4. **列标题处理增强**
```php
// 确保列名是字符串
foreach ($headerRow[0] as $column) {
    if (is_object($column) && method_exists($column, '__toString')) {
        $columns[] = (string)$column;
    } elseif (is_scalar($column)) {
        $columns[] = trim((string)$column);
    }
}
```

### 调试步骤

#### 1. **检查Excel文件格式**
- 确保第3行是列标题
- 确保列标题是文本格式，不是公式
- 避免合并单元格

#### 2. **验证数据类型**
- 数值字段应该是数字格式
- 文本字段应该是文本格式
- 避免特殊字符和公式

#### 3. **测试小文件**
- 先用2-3行数据测试
- 逐步增加数据量
- 确认基本功能正常

### 常见问题排查

#### Q1: 仍然出现 "Illegal offset type" 错误
**解决方案：**
1. 检查Excel文件是否有隐藏字符
2. 重新保存Excel文件为.xlsx格式
3. 确保列标题没有特殊字符

#### Q2: 部门名称无法识别
**解决方案：**
1. 确保部门名称与系统中完全一致
2. 检查是否有多余的空格
3. 可以使用部门ID代替部门名称

#### Q3: 数据导入部分成功
**解决方案：**
1. 查看错误详情确定具体问题
2. 修正有问题的数据行
3. 重新导入修正后的数据

### Excel文件要求

#### 1. **文件格式**
- 推荐使用 .xlsx 格式
- 避免使用 .xls 格式（兼容性问题）
- 文件大小不超过50MB

#### 2. **数据格式**
```
第1行：标题（可选）
第2行：空行（可选）
第3行：列标题（必须）
第4行开始：数据行
```

#### 3. **列标题要求**
- 必须是纯文本
- 不能包含公式
- 不能是合并单元格
- 建议使用中文标题

#### 4. **数据要求**
- 数值字段使用数字格式
- 文本字段使用文本格式
- 日期字段使用日期格式
- 避免空行

### 测试用例

#### 最小测试文件
```
第1行：农户电站导入测试
第2行：（空行）
第3行：序号 | 姓名 | 安装地址 | 部室
第4行：001 | 张三 | 测试地址1 | 技术部
第5行：002 | 李四 | 测试地址2 | 运维部
```

#### 完整测试文件
包含所有20个字段的测试数据，确保每种数据类型都有覆盖。

### 性能监控

#### 内存使用监控
```php
echo "处理前内存: " . memory_get_usage(true) / 1024 / 1024 . "MB\n";
// 处理逻辑
echo "处理后内存: " . memory_get_usage(true) / 1024 / 1024 . "MB\n";
```

#### 处理时间监控
```php
$startTime = microtime(true);
// 处理逻辑
$endTime = microtime(true);
echo "处理时间: " . ($endTime - $startTime) . "秒\n";
```

### 错误日志

如果问题仍然存在，请检查以下日志：
1. PHP错误日志
2. 应用程序日志
3. 数据库错误日志

### 联系支持

如果以上方法都无法解决问题，请提供：
1. 具体的错误信息
2. 测试用的Excel文件
3. PHP版本和环境信息
4. 错误发生的具体步骤
