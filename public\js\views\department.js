// 部门管理组件
const DepartmentManagement = {
    template: `
        <div class="department-management">
            <!-- 工具栏 -->
            <div class="toolbar" style="margin-bottom: 20px;">
                <el-button type="primary" @click="showCreateDialog()">新增部门</el-button>
            </div>

            <!-- 部门树形表格 -->
            <el-table
                v-loading="loading"
                :data="tableData"
                row-key="id"
                border
                default-expand-all
                :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
                style="width: 100%">
                <el-table-column prop="name" label="部门名称" min-width="200"></el-table-column>
                <el-table-column prop="project_year" label="项目年度" width="120"></el-table-column>
                <el-table-column prop="sort" label="排序" width="100"></el-table-column>
                <el-table-column label="操作" width="280" fixed="right">
                    <template slot-scope="scope">
                        <el-button size="small" @click="showCreateDialog(scope.row)">添加子部门</el-button>
                        <el-button size="small" type="primary" @click="showEditDialog(scope.row)">编辑</el-button>
                        <el-button size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>

            <!-- 部门表单对话框 -->
            <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="500px" @close="handleDialogClose">
                <el-form :model="form" :rules="rules" ref="form" label-width="100px">
                    <el-form-item label="部门编码" prop="code">
                        <el-input v-model="form.code"></el-input>
                    </el-form-item>
                    <el-form-item label="部门名称" prop="name">
                        <el-input v-model="form.name"></el-input>
                    </el-form-item>
                    <el-form-item label="上级部门">
                        <el-cascader
                            v-model="form.parent_id"
                            :options="departmentOptions"
                            :props="{
                                checkStrictly: true,
                                label: 'name',
                                value: 'id',
                                emitPath: false
                            }"
                            clearable
                            placeholder="请选择上级部门">
                        </el-cascader>
                    </el-form-item>
                    <el-form-item label="项目年度">
                        <el-input-number v-model="form.project_year" :min="2000" :max="2099" placeholder="请输入项目年度"></el-input-number>
                    </el-form-item>
                    <el-form-item label="排序" prop="sort">
                        <el-input-number v-model="form.sort" :min="0" :max="999"></el-input-number>
                    </el-form-item>
                </el-form>
                <div slot="footer" class="dialog-footer">
                    <el-button @click="dialogVisible = false">取 消</el-button>
                    <el-button type="primary" @click="handleSubmit">确 定</el-button>
                </div>
            </el-dialog>
        </div>
    `,
    data() {
        return {
            loading: false,
            tableData: [],
            dialogVisible: false,
            dialogTitle: '',
            form: {
                id: '',
                code: '',
                name: '',
                parent_id: 0,
                project_year: null,
                sort: 0
            },
            rules: {
                code: [
                    { required: true, message: '请输入部门编码', trigger: 'blur' },
                    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
                ],
                name: [
                    { required: true, message: '请输入部门名称', trigger: 'blur' },
                    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
                ]
            },
            departmentOptions: []
        }
    },
    created() {
        this.fetchData();
    },
    methods: {
        // 获取部门树数据
        fetchData() {
            this.loading = true;
            this.$http.get('/department/tree')
                .then(response => {
                    if (response.data.code === 200) {
                        // 对部门数据进行排序
                        const sortDepartments = (departments) => {
                            return departments.sort((a, b) => a.sort - b.sort).map(department => {
                                if (department.children && department.children.length > 0) {
                                    department.children = sortDepartments(department.children);
                                }
                                return department;
                            });
                        };
                        // 对顶级部门进行排序
                        this.tableData = sortDepartments(response.data.data);
                        // 对部门选项进行排序
                        this.departmentOptions = [{
                            id: 0,
                            name: '顶级部门',
                            children: sortDepartments(response.data.data)
                        }];
                    } else {
                        this.$message.error(response.data.message);
                    }
                })
                .catch(error => {
                    console.error('获取部门列表失败:', error);
                    this.$message.error('获取部门列表失败');
                })
                .finally(() => {
                    this.loading = false;
                });
        },

        // 显示创建对话框
        showCreateDialog(row = null) {
            this.dialogTitle = row ? `添加 ${row.name} 的子部门` : '新增部门';
            this.form = {
                id: '',
                code: '',
                name: '',
                parent_id: row ? row.id : 0,
                project_year: null,
                sort: 0
            };
            this.dialogVisible = true;
        },

        // 显示编辑对话框
        showEditDialog(row) {
            this.dialogTitle = '编辑部门';
            this.form = { ...row };
            this.dialogVisible = true;
        },

        // 关闭对话框
        handleDialogClose() {
            this.$refs.form.resetFields();
        },

        // 提交表单
        handleSubmit() {
            this.$refs.form.validate(valid => {
                if (valid) {
                    const method = this.form.id ? 'put' : 'post';
                    const url = this.form.id ? `/department/${this.form.id}` : '/department';
                    
                    this.$http[method](url, this.form)
                        .then(response => {
                            if (response.data.code === 200) {
                                this.$message.success(response.data.message);
                                this.dialogVisible = false;
                                this.fetchData();
                            } else {
                                this.$message.error(response.data.message);
                            }
                        })
                        .catch(error => {
                            console.error('操作失败:', error);
                            this.$message.error('操作失败');
                        });
                }
            });
        },

        // 删除部门
        handleDelete(row) {
            this.$confirm('确认删除该部门?', '提示', {
                type: 'warning'
            }).then(() => {
                this.$http.delete(`/department/${row.id}`)
                    .then(response => {
                        if (response.data.code === 200) {
                            this.$message.success(response.data.message);
                            this.fetchData();
                        } else {
                            this.$message.error(response.data.message);
                        }
                    })
                    .catch(error => {
                        console.error('删除失败:', error);
                        this.$message.error('删除失败');
                    });
            });
        }
    }
};

// 注册组件
Vue.component('department-management', DepartmentManagement); 