-- 为部门表添加项目年度字段
-- 执行时间：2025年1月
-- 说明：在系统管理-部门管理中增加项目年度功能

-- 检查字段是否已存在，如果不存在则添加
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'sp_department' 
     AND COLUMN_NAME = 'project_year') = 0,
    'ALTER TABLE `sp_department` ADD COLUMN `project_year` int(11) DEFAULT NULL COMMENT ''项目年度'' AFTER `parent_id`, ADD KEY `idx_project_year` (`project_year`);',
    'SELECT ''字段 project_year 已存在，跳过添加'';'
));

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 验证字段是否添加成功
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'sp_department' 
AND COLUMN_NAME = 'project_year';
