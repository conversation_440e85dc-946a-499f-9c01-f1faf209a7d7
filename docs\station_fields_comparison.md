# sp_station 表字段对比分析

## 目标字段列表
```
序号、部室、原部室、项目建设年份、部室档案序号、姓名、国网户号、身份证号、电话、县区、乡镇、安装地址、组件品牌、组件数量、组件功率、电站容量、逆变器品牌、逆变器序列号、固定收益、异常电站备注（电站状态：拆除、资产减除、电站更换）
```

## 当前数据库字段 vs 目标字段对比

| 目标字段 | 当前数据库字段 | 字段名 | 类型 | 状态 |
|---------|---------------|--------|------|------|
| 序号 | ✅ 存在 | `total_serial` | varchar(255) | 匹配 |
| 部室 | ✅ 存在 | `department_id` | int(11) | 匹配（关联部门表） |
| 原部室 | ❌ 缺失 | - | - | **需要添加** |
| 项目建设年份 | ❌ 缺失 | - | - | **需要添加** |
| 部室档案序号 | ✅ 存在 | `arc_id` | int(11) | 匹配 |
| 姓名 | ✅ 存在 | `contact_name` | varchar(50) | 匹配 |
| 国网户号 | ✅ 存在 | `gongwang_account` | varchar(255) | 匹配 |
| 身份证号 | ✅ 存在 | `id_card` | varchar(255) | 匹配 |
| 电话 | ✅ 存在 | `contact_phone` | varchar(50) | 匹配 |
| 县区 | ✅ 存在 | `county` | varchar(255) | 匹配 |
| 乡镇 | ✅ 存在 | `town` | varchar(100) | 匹配 |
| 安装地址 | ✅ 存在 | `address` | varchar(255) | 匹配 |
| 组件品牌 | ✅ 存在 | `component_brand` | varchar(255) | 匹配 |
| 组件数量 | ✅ 存在 | `component_count` | int(11) | 匹配 |
| 组件功率 | ✅ 存在 | `component_power` | decimal(10,0) | 匹配 |
| 电站容量 | ✅ 存在 | `capacity` | decimal(10,2) | 匹配 |
| 逆变器品牌 | ✅ 存在 | `inverter_brand` | varchar(255) | 匹配 |
| 逆变器序列号 | ✅ 存在 | `inverter_serial` | varchar(255) | 匹配 |
| 固定收益 | ✅ 存在 | `fixed_income` | decimal(10,0) | 匹配 |
| 异常电站备注 | ❌ 缺失 | - | - | **需要添加** |

## 需要添加的字段

### 1. 原部室字段
```sql
ALTER TABLE `sp_station` 
ADD COLUMN `original_department` varchar(100) DEFAULT NULL COMMENT '原部室' 
AFTER `department_id`;
```

### 2. 项目建设年份字段
```sql
ALTER TABLE `sp_station` 
ADD COLUMN `construction_year` year DEFAULT NULL COMMENT '项目建设年份' 
AFTER `original_department`;
```

### 3. 异常电站备注字段
```sql
ALTER TABLE `sp_station` 
ADD COLUMN `abnormal_remark` text DEFAULT NULL COMMENT '异常电站备注（电站状态：拆除、资产减除、电站更换）' 
AFTER `fixed_income`;
```

## 当前表中的额外字段

以下字段在目标列表中没有，但在当前表中存在：

| 字段名 | 类型 | 说明 | 建议 |
|--------|------|------|------|
| `id` | int(11) | 主键ID | 保留 |
| `station_name` | varchar(100) | 电站名称 | 保留 |
| `product_price` | decimal(10,2) | 产品价格 | 保留 |
| `archive_status` | varchar(50) | 档案接收情况 | 保留 |
| `bank_card_status` | varchar(50) | 银行卡接收状态 | 保留 |
| `electricity_card` | varchar(100) | 电费卡卡号 | 保留 |
| `bank_name` | varchar(100) | 电费卡开户行 | 保留 |
| `bank_card` | varchar(100) | 最新银行卡号 | 保留 |
| `bank_branch` | varchar(100) | 银行卡开户行 | 保留 |
| `card_status` | varchar(50) | 电站电费卡状态 | 保留 |
| `card_open_time` | varchar(50) | 开卡时间 | 保留 |
| `grid_contract_status` | varchar(50) | 国网签约状态 | 保留 |
| `install_date` | date | 并网日期 | 保留 |
| `latitude` | decimal(10,6) | 纬度 | 保留 |
| `longitude` | decimal(10,6) | 经度 | 保留 |
| `status` | tinyint(1) | 状态 | 保留 |
| `type` | tinyint(1) | 电站类型 | 保留 |
| `business_type` | varchar(50) | 企业类型 | 保留 |
| `contract_number` | varchar(50) | 合同编号 | 保留 |
| `village` | varchar(100) | 村名 | 保留 |
| `create_at` | datetime | 创建时间 | 保留 |
| `update_at` | datetime | 更新时间 | 保留 |
| `is_deleted` | tinyint(1) | 删除标记 | 保留 |

## 总结

### ✅ 已匹配字段：17个
大部分核心字段已经存在并匹配。

### ❌ 缺失字段：3个
1. **原部室** - 需要添加
2. **项目建设年份** - 需要添加  
3. **异常电站备注** - 需要添加

### 📊 匹配率：85%
17/20 = 85% 的字段已经匹配。

## 建议操作

1. 执行上述3个ALTER TABLE语句添加缺失字段
2. 保留现有的额外字段，它们提供了更丰富的功能
3. 考虑在前端界面中展示这些新增字段

---

## 农户电站导入功能更新

### ✅ 已完成的修改

#### 1. **导入配置更新** (`config/import.php`)
- 添加了3个新字段的映射配置

#### 2. **前端表单更新** (`public/js/views/station-farmer.js`)
- 添加了3个新字段的表单控件
- 更新了表单初始化和编辑逻辑

#### 3. **后端处理更新** (`app/controller/StationImportController.php`)
- 增强了导入数据处理逻辑

#### 4. **模型更新** (`app/model/Station.php`)
- 更新了模型schema，添加了所有新字段的定义

#### 5. **数据库结构更新**
- 更新了表结构定义文件
- 创建了迁移脚本

### 🎯 **最终结论**

✅ **农户电站导入功能已完全支持目标字段列表中的所有20个字段**
✅ **前端表单已更新，支持手动录入新字段**
✅ **后端处理逻辑已优化，支持智能数据处理**
✅ **数据库结构已更新，完全匹配需求**

现在农户电站管理功能已经完全满足你的字段需求！
