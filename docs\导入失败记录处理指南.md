# 农户电站导入失败记录处理指南

## 新功能介绍

系统已升级为逐条导入模式，即使部分数据有问题，也不会影响其他正确数据的导入。同时提供详细的失败记录信息和导出功能。

## 导入结果展示

### 成功率显示
导入完成后，系统会显示：
- 成功导入的记录数
- 失败的记录数
- 整体成功率

### 失败记录详情表格
系统会以表格形式显示失败记录的关键信息：

| 列名 | 说明 | 示例 |
|------|------|------|
| 行号 | Excel文件中的行号 | 1454 |
| 序号 | 电站序号 | 001 |
| 姓名 | 联系人姓名 | 张三 |
| 国网户号 | 国网户号 | 3700979065707 |
| 电话 | 联系电话 | 18396724522 |
| 安装地址 | 电站地址 | 北官庄村25号 |
| 错误原因 | 简化的错误描述 | 数值字段包含文本 |

## 错误类型说明

### 1. 数值字段包含文本
**错误描述**：数值字段包含文本
**原因**：电站容量、组件功率、固定收益等数字字段中包含了文本内容
**示例**：电站容量字段出现"古瑞瓦特"（应该是逆变器品牌）
**解决方案**：
- 检查Excel文件的列顺序是否正确
- 确保数字字段只包含数字
- 将错位的文本数据移动到正确的列

### 2. 整数字段包含文本
**错误描述**：整数字段包含文本
**原因**：组件数量、档案序号等整数字段中包含了文本内容
**示例**：组件数量字段出现"向城镇何庄"（应该是地址）
**解决方案**：
- 检查数据是否在正确的列中
- 确保整数字段只包含整数
- 重新整理数据列顺序

### 3. 日期格式错误
**错误描述**：日期格式错误
**原因**：日期字段格式不正确
**解决方案**：
- 使用标准日期格式（YYYY-MM-DD）
- 检查日期字段是否包含无效数据

### 4. 数据为空
**错误描述**：数据为空
**原因**：处理后的数据为空，可能是必填字段缺失
**解决方案**：
- 检查必填字段是否有数据
- 确保行中至少有一些有效数据

## 失败记录处理流程

### 第一步：查看失败记录
1. 导入完成后，点击弹出对话框中的详情
2. 查看失败记录表格，了解具体的失败原因
3. 注意观察错误模式，是否有规律性问题

### 第二步：导出失败记录
1. 在失败记录详情对话框中，点击"导出失败记录"按钮
2. 系统会自动下载CSV文件，文件名格式：`农户电站导入失败记录_YYYY-MM-DD.csv`
3. 使用Excel打开CSV文件，查看详细的失败记录

### 第三步：分析问题原因
根据失败记录的错误原因，分析问题类型：

**列位置错乱问题**：
- 如果大量记录显示"数值字段包含文本"
- 说明Excel文件的列顺序与标准模板不一致
- 需要重新整理Excel文件的列顺序

**数据质量问题**：
- 如果是零散的错误
- 可能是个别记录的数据质量问题
- 需要逐条修正数据

### 第四步：修正数据
根据问题类型采取相应的修正措施：

**方案A：重新整理Excel文件（推荐）**
1. 创建新的Excel文件，使用标准模板
2. 按照正确的列顺序重新组织数据
3. 确保每列的数据类型与列标题匹配

**方案B：修正失败记录**
1. 根据导出的失败记录CSV文件
2. 在原Excel文件中找到对应的行
3. 逐条修正数据问题
4. 重新导入修正后的数据

### 第五步：重新导入
1. 修正数据后，重新进行导入
2. 建议先用少量数据测试
3. 确认无误后再导入全部数据

## 最佳实践建议

### 1. 数据准备阶段
- 使用标准模板准备数据
- 确保列标题与模板完全一致
- 检查数据类型是否正确

### 2. 导入测试阶段
- 先导入前100行数据进行测试
- 检查导入结果和失败记录
- 根据测试结果调整数据格式

### 3. 正式导入阶段
- 在网络稳定的环境下进行
- 耐心等待导入完成
- 及时处理失败记录

### 4. 数据验证阶段
- 检查导入成功的数据是否正确
- 验证关键字段的数据完整性
- 必要时进行数据抽查

## 常见问题解答

### Q1：为什么会出现列位置错乱？
A1：通常是因为Excel文件的列标题顺序与系统期望的不一致，或者在数据整理过程中列的顺序发生了变化。

### Q2：如何避免数据类型错误？
A2：确保数字字段只包含数字，文本字段不要包含特殊字符，严格按照模板格式准备数据。

### Q3：失败记录可以重新导入吗？
A3：可以。修正失败记录的数据后，可以重新导入。系统会自动跳过重复的数据。

### Q4：导入过程中可以中断吗？
A4：不建议中断导入过程。如果必须中断，已成功导入的数据会保留，可以从失败的地方继续。

### Q5：如何提高导入成功率？
A5：
1. 严格按照标准模板准备数据
2. 进行小批量测试
3. 确保数据质量
4. 检查网络连接稳定性

## 技术支持

如果遇到以下情况，请联系技术支持：
- 大量数据出现相同错误
- 按照指南操作仍无法解决问题
- 系统出现异常错误
- 需要批量数据修正工具

联系时请提供：
- 失败记录的CSV文件
- Excel源文件的截图（前几行）
- 具体的错误描述
