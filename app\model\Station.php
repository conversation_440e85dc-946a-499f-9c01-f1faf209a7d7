<?php
declare (strict_types = 1);

namespace app\model;

use think\Model;

class Station extends Model
{
    protected $name = 'station';
    protected $prefix = 'sp_';
    
    protected $autoWriteTimestamp = true;
    protected $createTime = 'create_at';
    protected $updateTime = 'update_at';

    // 设置允许写入的字段
    protected $schema = [
        'id'                   => 'int',
        'total_serial'         => 'string',
        'station_name'         => 'string',
        'type'                 => 'int',
        'business_type'        => 'string',
        'contact_name'         => 'string',
        'contact_phone'        => 'string',
        'address'              => 'string',
        'capacity'             => 'float',
        'install_date'         => 'string',
        'contract_number'      => 'string',
        'department_id'        => 'int',
        'original_department'  => 'string',
        'construction_year'    => 'int',
        'gongwang_account'     => 'string',
        'id_card'              => 'string',
        'county'               => 'string',
        'town'                 => 'string',
        'component_brand'      => 'string',
        'component_count'      => 'int',
        'component_power'      => 'float',
        'inverter_brand'       => 'string',
        'inverter_serial'      => 'string',
        'fixed_income'         => 'float',
        'abnormal_remark'      => 'string',
        'latitude'             => 'string',
        'longitude'            => 'string',
        'status'               => 'int',
        'create_at'            => 'datetime',
        'update_at'            => 'datetime'
    ];

    // 设置字段信息
    protected $field = [
        'id', 
        'station_name',
        'type',
        'business_type',
        'contact_name',
        'contact_phone',
        'address',
        'capacity',
        'install_date',
        'contract_number',
        'department_id',
        'latitude',
        'longitude',
        'status',
        'create_at',
        'update_at'
    ];

    // 关联部门
    public function department()
    {
        return $this->belongsTo(Department::class, 'department_id', 'id');
    }

    // 电站类型
    const TYPE_FARMER = 1;  // 合作农户
    const TYPE_BUSINESS = 2; // 工商业

    // 获取电站类型文本
    public function getTypeTextAttr($value, $data)
    {
        $types = [
            self::TYPE_FARMER => '合作农户',
            self::TYPE_BUSINESS => '工商业'
        ];
        return isset($types[$data['type']]) ? $types[$data['type']] : '未知';
    }

    // 搜索器：电站类型
    public function searchTypeAttr($query, $value)
    {
        if ($value) {
            $query->where('type', $value);
        }
    }

    // 搜索器：关键字搜索
    public function searchKeywordAttr($query, $value)
    {
        if ($value) {
            $query->where('station_name|address', 'like', "%{$value}%");
        }
    }

    // 搜索器：部门筛选
    public function searchDepartmentIdAttr($query, $value)
    {
        if ($value) {
            $query->where('department_id', $value);
        }
    }

    // 添加全局查询范围，筛选未删除记录
    public static function onBeforeQuery($query)
    {
        $query->where('is_deleted', 0);
    }
} 