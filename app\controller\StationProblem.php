<?php
namespace app\controller;

use think\App;
use app\model\StationProblem as StationProblemModel;
use app\service\LogService;
use think\facade\Session;
use think\facade\Db;
use think\facade\Filesystem;
use think\file\UploadedFile;
use think\Request;
use think\facade\Log;

class StationProblem extends \app\BaseController
{
    protected $stationProblemModel;

    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->stationProblemModel = new StationProblemModel();
    }

    /**
     * 获取问题电站列表
     */
    public function list()
    {
        try {
            $params = $this->request->get();
            $page = isset($params['page']) ? intval($params['page']) : 1;
            $limit = isset($params['limit']) ? intval($params['limit']) : 10;
            $keyword = isset($params['keyword']) ? trim($params['keyword']) : '';
            $problemType = isset($params['problemType']) ? trim($params['problemType']) : '';
            $status = isset($params['status']) ? trim($params['status']) : '';

            $where = [];
            // 当使用alias时，模型的全局查询范围可能不会应用，需要手动添加软删除条件
            $where[] = ['problem.is_deleted', '=', 0];

            if ($keyword) {
                $where[] = ['station.gongwang_account|station.contact_name|station.contact_phone', 'like', "%{$keyword}%"];
            }
            if ($problemType) {
                $where[] = ['problem.problem_type', '=', $problemType];
            }
            if ($status) {
                $where[] = ['problem.status', '=', $status];
            }

            $result = $this->stationProblemModel->alias('problem')
                ->join('sp_station station', 'station.id = problem.station_id')
                ->leftJoin('sp_station_problem_handler handler', 'handler.problem_id = problem.id')
                ->leftJoin('sp_user user', 'user.id = handler.handler_id')
                ->leftJoin('sp_department dept', 'dept.id = station.department_id')
                ->where($where)
                ->field('problem.*, station.gongwang_account as gongwangAccount, station.contact_name as contactName, 
                         station.contact_phone as contactPhone, station.address as stationAddress, 
                         station.total_serial as total_serial, dept.name as departmentName, 
                         GROUP_CONCAT(DISTINCT user.realname) as handlerNames')
                ->group('problem.id')
                ->order('problem.register_time', 'desc')
                ->page($page, $limit)
                ->select();

            $total = $this->stationProblemModel->alias('problem')
                ->join('sp_station station', 'station.id = problem.station_id')
                ->where($where)
                ->count();

            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => [
                    'items' => $result,
                    'total' => $total
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取运维列表失败: ' . $e->getMessage());
            return json([
                'code' => 500,
                'message' => '获取失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 登记问题电站
     */
    public function register()
    {
        try {
            $params = $this->request->post();
            if (empty($params['stationId']) || empty($params['problemType']) || 
                empty($params['description'])) {
                return json(['code' => 400, 'message' => '缺少必要参数 (电站ID, 问题类型, 问题描述)']);
            }
            if (empty($params['handlers']) || !is_array($params['handlers']) || count($params['handlers']) === 0) {
                 return json(['code' => 400, 'message' => '请至少选择一个处理人员']);
            }

            // 开始事务
            Db::startTrans();
            
            $data = [
                'station_id' => intval($params['stationId']),
                'problem_type' => $params['problemType'],
                'description' => $params['description'],
                'status' => 'pending',
                'register_time' => date('Y-m-d H:i:s')
            ];

            // 保存问题记录
            $problemId = $this->stationProblemModel->insertGetId($data);
            
            if (!$problemId) {
                Db::rollback();
                Log::error('登记运维记录失败: 数据库插入失败');
                return json(['code' => 500, 'message' => '登记失败']);
            }
            
            // 保存处理人
            $handlerData = [];
            foreach ($params['handlers'] as $handlerId) {
                if (!empty($handlerId) && is_numeric($handlerId)) {
                    $handlerData[] = [
                        'problem_id' => $problemId,
                        'handler_id' => intval($handlerId),
                        'create_at' => date('Y-m-d H:i:s')
                    ];
                }
            }
            
            if (!empty($handlerData)) {
                $result = Db::name('station_problem_handler')->insertAll($handlerData);
                if (!$result) {
                    Db::rollback();
                    Log::error('保存处理人失败: problem_id=' . $problemId);
                    return json(['code' => 500, 'message' => '保存处理人失败']);
                }
            } else {
                 Db::rollback();
                 Log::warning('未找到有效的处理人ID: problem_id=' . $problemId . ' handlers=' . json_encode($params['handlers']));
                 return json(['code' => 400, 'message' => '未找到有效的处理人信息']);
            }
            
            // 提交事务
            Db::commit();
            
            return json(['code' => 200, 'message' => '登记成功']);
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('登记运维记录异常: ' . $e->getMessage());
            return json(['code' => 500, 'message' => '登记失败：' . $e->getMessage()]);
        }
    }
    
    /**
     * 删除问题电站
     */
    public function delete()
    {
        try {
            $id = $this->request->post('id');
            if (empty($id) || !is_numeric($id)) {
                return json(['code' => 400, 'message' => '无效的记录ID']);
            }
            
            $problemId = intval($id);

            // 开始事务 (如果需要同时删除关联数据)
            // Db::startTrans(); 
            
            // 软删除问题记录（设置is_deleted为1）
            $result = $this->stationProblemModel->where('id', $problemId)
                ->update(['is_deleted' => 1, 'update_time' => date('Y-m-d H:i:s')]); // 记录更新时间
            
            // $result 可能为 0 (记录不存在或已删除) 或 false (失败)
            if ($result === false) {
                // Db::rollback();
                Log::error('软删除运维记录失败: problem_id=' . $problemId);
                return json(['code' => 500, 'message' => '删除失败']);
            }
             if ($result === 0) {
                 // Db::rollback(); // or commit? Decide based on whether related data needs deletion
                 Log::warning('尝试删除不存在或已删除的记录: problem_id=' . $problemId);
                 return json(['code' => 404, 'message' => '记录不存在或已被删除']);
             }
            
            // 按需删除关联数据，例如处理人、档案等
            // Db::name('station_problem_handler')->where('problem_id', $problemId)->delete();
            // $archives = Db::name('station_problem_archive')->where('problem_id', $problemId)->select();
            // foreach ($archives as $archive) {
                 // $this->deleteArchiveFile($archive['file_path']);
            // }
            // Db::name('station_problem_archive')->where('problem_id', $problemId)->delete();


            // Db::commit();
            
            return json(['code' => 200, 'message' => '删除成功']);
        } catch (\Exception $e) {
            // Db::rollback();
            Log::error('删除运维记录异常: ' . $e->getMessage());
            return json(['code' => 500, 'message' => '删除失败：' . $e->getMessage()]);
        }
    }
    
    /**
     * 获取部门用户
     */
    public function getDepartmentUsers()
    {
        try {
            $departmentId = $this->request->param('departmentId');
            if (empty($departmentId) || !is_numeric($departmentId)) {
                return json(['code' => 400, 'message' => '无效的部门ID']);
            }
            
            $users = Db::name('user')
                ->where('department_id', intval($departmentId))
                ->where('status', 1) // 只获取有效用户
                ->field('id, realname')
                ->select();
                
            return json(['code' => 200, 'message' => '获取成功', 'data' => $users]);
        } catch (\Exception $e) {
            Log::error('获取部门用户失败: ' . $e->getMessage());
            return json(['code' => 500, 'message' => '获取失败：' . $e->getMessage()]);
        }
    }
    
    /**
     * 获取所有部门用户
     */
    public function getAllDepartmentUsers()
    {
        try {
            $cacheKey = 'all_department_users_active';
            $cacheData = cache($cacheKey);
            
            if ($cacheData) {
                return json(['code' => 200, 'message' => '获取成功(缓存)', 'data' => $cacheData]);
            }
            
            $departments = Db::name('department')
                 ->where('status', 1)
                 ->field('id, name')
                 ->order('sort desc, id asc')
                 ->select();
                
            $users = Db::name('user')
                ->field('id, realname, department_id')
                ->where('status', 1) 
                ->select()
                ->toArray();
                
            $usersByDept = [];
            foreach ($users as $user) {
                $deptId = $user['department_id'];
                if (!isset($usersByDept[$deptId])) {
                    $usersByDept[$deptId] = [];
                }
                $usersByDept[$deptId][] = ['id' => $user['id'], 'realname' => $user['realname']]; 
            }
            
            $result = [];
            foreach ($departments as $dept) {
                $deptId = $dept['id'];
                $result[$deptId] = [
                    'department' => $dept,
                    'users' => isset($usersByDept[$deptId]) ? $usersByDept[$deptId] : []
                ];
            }
            
            cache($cacheKey, $result, 300); 
            
            return json(['code' => 200, 'message' => '获取成功', 'data' => $result]);
        } catch (\Exception $e) {
            Log::error('获取所有部门用户失败: ' . $e->getMessage());
            return json(['code' => 500, 'message' => '获取失败：' . $e->getMessage()]);
        }
    }
    
    /**
     * 获取电站信息
     */
    public function getStationsByGuowang()
    {
        try {
            $keyword = $this->request->param('keyword', '');
            
            $stations = Db::name('station')
                ->where('gongwang_account', 'like', "%{$keyword}%")
                 ->where('status', 1)
                ->field('id, gongwang_account as guowang_account, contact_name, contact_phone, address')
                ->limit(10)
                ->select();
                
            return json(['code' => 200, 'message' => '获取成功', 'data' => $stations]);
        } catch (\Exception $e) {
            Log::error('根据国网号获取电站失败: ' . $e->getMessage());
            return json(['code' => 500, 'message' => '获取失败：' . $e->getMessage()]);
        }
    }
    
    /**
     * 获取部门列表
     */
    public function getDepartments()
    {
        try {
            $departments = Db::name('department')
                ->where('parent_id', '<>', 0) 
                ->where('status', 1)
                ->field('id, name')
                ->order('sort desc, id asc')
                ->select();
                
            return json(['code' => 200, 'message' => '获取成功', 'data' => $departments]);
        } catch (\Exception $e) {
            Log::error('获取部门列表失败: ' . $e->getMessage());
            return json(['code' => 500, 'message' => '获取失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取电站列表（用于下拉选择）
     */
    public function index()
    {
        try {
            $cacheKey = 'station_list_for_select_active';
            $cacheData = cache($cacheKey);
            
            if ($cacheData) {
                return json(['code' => 200, 'message' => '获取成功(缓存)', 'data' => $cacheData]);
            }
            
            $stations = Db::name('station')
                ->where('status', 1)
                ->field('id, station_name, gongwang_account, contact_name, contact_phone, address, total_serial')
                 ->order('total_serial asc, id asc')
                ->select();
                
            cache($cacheKey, $stations, 600); 
            
            return json(['code' => 200, 'message' => '获取成功', 'data' => $stations]);
        } catch (\Exception $e) {
            Log::error('获取电站下拉列表失败: ' . $e->getMessage());
            return json(['code' => 500, 'message' => '获取失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取问题处理人
     */
    public function getHandlers()
    {
        try {
            $problemId = $this->request->param('problemId');
            if (empty($problemId) || !is_numeric($problemId)) {
                return json(['code' => 400, 'message' => '无效的记录ID']);
            }
            
            $handlers = Db::name('station_problem_handler')
                ->alias('handler')
                ->leftJoin('sp_user user', 'user.id = handler.handler_id')
                ->leftJoin('sp_department dept', 'dept.id = user.department_id')
                ->where('handler.problem_id', intval($problemId))
                 ->where('user.status', 1)
                ->field('handler.handler_id, user.realname, user.department_id, dept.name as department_name')
                ->select();
                
            return json(['code' => 200, 'message' => '获取成功', 'data' => $handlers]);
        } catch (\Exception $e) {
            Log::error('获取问题处理人失败: ' . $e->getMessage());
            return json(['code' => 500, 'message' => '获取失败：' . $e->getMessage()]);
        }
    }

    /**
     * 更新问题电站
     */
    public function update()
    {
        try {
            $params = $this->request->post();
            if (empty($params['id']) || empty($params['stationId']) || empty($params['problemType']) || 
                empty($params['description'])) {
                return json(['code' => 400, 'message' => '缺少必要参数 (记录ID, 电站ID, 问题类型, 问题描述)']);
            }
             if (empty($params['handlers']) || !is_array($params['handlers']) || count($params['handlers']) === 0) {
                 return json(['code' => 400, 'message' => '请至少选择一个处理人员']);
            }
            
            $problemId = intval($params['id']);

            // 开始事务
            Db::startTrans();
            
            $data = [
                'problem_type' => $params['problemType'],
                'description' => $params['description'],
                'update_time' => date('Y-m-d H:i:s')
            ];

            // 更新问题记录
            $result = $this->stationProblemModel->where('id', $problemId)->update($data);
            
            // $result 可能为 0 (无变化) 或 false (失败)
            if ($result === false) {
                Db::rollback();
                Log::error('更新运维记录失败: problem_id=' . $problemId);
                return json(['code' => 500, 'message' => '更新失败']);
            }
            
            // 更新处理人（先删除旧的，再添加新的）
            Db::name('station_problem_handler')->where('problem_id', $problemId)->delete();
            
            // 保存处理人
            $handlerData = [];
             foreach ($params['handlers'] as $handlerId) {
                if (!empty($handlerId) && is_numeric($handlerId)) {
                    $handlerData[] = [
                        'problem_id' => $problemId,
                        'handler_id' => intval($handlerId),
                        'create_at' => date('Y-m-d H:i:s')
                    ];
                }
            }
            
            if (!empty($handlerData)) {
                $result = Db::name('station_problem_handler')->insertAll($handlerData);
                if (!$result) {
                    Db::rollback();
                     Log::error('更新处理人失败: problem_id=' . $problemId);
                    return json(['code' => 500, 'message' => '保存处理人失败']);
                }
            } else {
                 Db::rollback();
                 Log::warning('更新时未找到有效的处理人ID: problem_id=' . $problemId . ' handlers=' . json_encode($params['handlers']));
                 return json(['code' => 400, 'message' => '未找到有效的处理人信息']);
            }
            
            // 提交事务
            Db::commit();
            
            return json(['code' => 200, 'message' => '更新成功']);
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('更新运维记录异常: ' . $e->getMessage());
            return json(['code' => 500, 'message' => '更新失败：' . $e->getMessage()]);
        }
    }

    // --- 运维档案管理方法 ---

    /**
     * 获取指定运维记录的档案列表
     * GET /problem_station/:problemId/archives
     */
    public function archiveList($problemId)
    {
        try {
            if (empty($problemId) || !is_numeric($problemId)) {
                 return json(['code' => 400, 'message' => '无效的运维记录ID']);
            }
            
            $problemExists = Db::name('station_problem')->where('id', $problemId)->where('is_deleted', 0)->count();
            if (!$problemExists) {
                 return json(['code' => 404, 'message' => '指定的运维记录不存在']);
            }

            $archives = Db::name('station_problem_archive')
                ->where('problem_id', $problemId)
                ->where('is_deleted', 0) // 只查询未删除的档案
                ->order('create_at', 'desc')
                ->select()
                ->map(function ($item) {
                    $item['url'] = request()->domain() . '/uploads/' . $item['file_path']; 
                    return $item;
                });
            
            return json(['code' => 200, 'message' => '获取成功', 'data' => $archives]);
        } catch (\Exception $e) {
            Log::error("获取运维档案列表失败 (Problem ID: {$problemId}): " . $e->getMessage());
            return json(['code' => 500, 'message' => '获取档案列表失败']);
        }
    }

    /**
     * 上传运维档案文件
     * POST /problem_station/:problemId/archives
     */
    public function archiveUpload(Request $request, $problemId)
    {
        if (empty($problemId) || !is_numeric($problemId)) {
             return json(['code' => 400, 'message' => '无效的运维记录ID']);
        }

        $problemExists = Db::name('station_problem')->where('id', $problemId)->where('is_deleted', 0)->count();
        if (!$problemExists) {
             return json(['code' => 404, 'message' => '指定的运维记录不存在或已被删除']);
        }

        try {
            $file = $request->file('file');
            $name = $request->param('name');

            if (!$file) {
                 Log::warning("运维档案上传失败 (Problem ID: {$problemId}): 未接收到文件");
                return json(['code' => 400, 'message' => '请选择要上传的文件']);
            }
             if (empty($name)) {
                 Log::warning("运维档案上传失败 (Problem ID: {$problemId}): 未提供文件名称");
                 return json(['code' => 400, 'message' => '请输入文件名称']);
            }

            if (!$file->isValid()) {
                $errorMsg = $file->getErrorMessage();
                Log::error("运维档案上传失败 (Problem ID: {$problemId}): 文件无效 - {$errorMsg}");
                return json(['code' => 400, 'message' => '文件无效：' . $errorMsg]);
            }

            $fileType = $file->getMime();
            $allowedMimes = [
                'image/jpeg', 'image/png', 'image/gif', 'image/jpg', 'image/webp', 
                'application/pdf',
                'audio/mpeg',
                'audio/mp3',
                'audio/ogg', 
                'audio/wav', 
                'audio/aac',
                'video/mp4', 
                'video/ogg', 
                'video/webm', 
                'video/avi', 
                'video/mov',
                'video/quicktime'
            ];
            if (!in_array($fileType, $allowedMimes)) {
                Log::warning("运维档案上传失败 (Problem ID: {$problemId}): 文件类型不允许 - {$fileType}");
                return json(['code' => 400, 'message' => '不支持的文件类型，仅允许图片、PDF、音频(MP3/WAV/AAC/OGG)和视频(MP4/WEBM/AVI/MOV/OGG)']);
            }

            $fileSize = $file->getSize();
            $maxSize = 50 * 1024 * 1024;
            if ($fileSize > $maxSize) {
                 Log::warning("运维档案上传失败 (Problem ID: {$problemId}): 文件过大 - {$fileSize} bytes");
                return json(['code' => 400, 'message' => '文件大小不能超过50MB']);
            }

            $saveDir = 'problem_archive/' . date('Y/m/d');
            $savePath = root_path() . 'public/uploads/' . $saveDir;
            if (!is_dir($savePath)) {
                if (!mkdir($savePath, 0777, true)) {
                     Log::error("运维档案上传失败 (Problem ID: {$problemId}): 创建存储目录失败 - {$savePath}");
                    return json(['code' => 500, 'message' => '服务器错误：无法创建存储目录']);
                }
            }

            $fileName = md5(uniqid((string)mt_rand(), true)) . '.' . $file->getOriginalExtension();
            $relativePath = $saveDir . '/' . $fileName;

            try {
                 $file->move($savePath, $fileName);
                 Log::info("运维档案上传成功 (Problem ID: {$problemId}): 文件已保存 - {$relativePath}");
            } catch (\Exception $e) {
                 Log::error("运维档案上传失败 (Problem ID: {$problemId}): 文件移动失败 - {$e->getMessage()}");
                return json(['code' => 500, 'message' => '文件保存失败：' . $e->getMessage()]);
            }

            try {
                $data = [
                    'problem_id' => $problemId,
                    'name' => $name,
                    'file_path' => $relativePath,
                    'file_size' => $fileSize,
                    'file_type' => $fileType,
                    'create_at' => date('Y-m-d H:i:s')
                ];
                 $archiveId = Db::name('station_problem_archive')->insertGetId($data);
                if (!$archiveId) {
                     Log::error("运维档案上传失败 (Problem ID: {$problemId}): 数据库记录保存失败 - " . json_encode($data));
                     $this->deleteArchiveFile($relativePath);
                    return json(['code' => 500, 'message' => '保存档案记录失败']);
                }
                 Log::info("运维档案数据库记录保存成功 (Problem ID: {$problemId}, Archive ID: {$archiveId})");
            } catch (\Exception $e) {
                 Log::error("运维档案上传失败 (Problem ID: {$problemId}): 保存数据库记录异常 - {$e->getMessage()}");
                 $this->deleteArchiveFile($relativePath);
                return json(['code' => 500, 'message' => '保存档案记录时发生错误']);
            }

            return json(['code' => 200, 'message' => '上传成功']);

        } catch (\Exception $e) {
             Log::error("运维档案上传处理异常 (Problem ID: {$problemId}): " . $e->getMessage());
            return json(['code' => 500, 'message' => '上传失败：' . $e->getMessage()]);
        }
    }

    /**
     * 删除运维档案文件
     * DELETE /problem_station/:problemId/archives/:archiveId
     */
    public function archiveDelete($archiveId)
    {
         if (empty($archiveId) || !is_numeric($archiveId)) {
             return json(['code' => 400, 'message' => '无效的ID']);
        }

        try {
            $archive = Db::name('station_problem_archive')
                ->where('id', $archiveId)
                ->where('is_deleted', 0) // 只查找未删除的记录
                ->find();

            if (!$archive) {
                 Log::warning("尝试删除不存在的运维档案 (Archive ID: {$archiveId})");
                return json(['code' => 404, 'message' => '档案文件不存在']);
            }

            // 软删除：设置 is_deleted = 1
            $result = Db::name('station_problem_archive')
                ->where('id', $archiveId)
                ->update([
                    'is_deleted' => 1,
                    'update_at' => date('Y-m-d H:i:s') // 记录删除时间
                ]);

            if (!$result) {
                 Log::error("软删除运维档案数据库记录失败 (Archive ID: {$archiveId})");
                return json(['code' => 500, 'message' => '删除数据库记录失败']);
            }

            // 注意：不再删除物理文件，保留文件以便恢复
            // 如果需要定期清理，可以通过定时任务处理已软删除的文件

            Log::info("运维档案软删除成功 (Archive ID: {$archiveId})");
            return json(['code' => 200, 'message' => '删除成功']);

        } catch (\Exception $e) {
             Db::rollback(); // 确保异常时回滚
             Log::error("删除运维档案异常 (Archive ID: {$archiveId}): " . $e->getMessage());
            return json(['code' => 500, 'message' => '删除失败：' . $e->getMessage()]);
        }
    }

    /**
     * 辅助方法：删除物理文件
     * @param string $relativePath 文件相对路径 (e.g., 'problem_archive/2023/10/26/...')
     * @return bool 是否成功删除
     */
    private function deleteArchiveFile($relativePath)
    {
        if (empty($relativePath)) {
            return false;
        }
        $filePath = root_path() . 'public/uploads/' . $relativePath;
        try {
            if (file_exists($filePath)) {
                if (!unlink($filePath)) {
                    Log::error("删除文件失败 (unlink failed): {$filePath}");
                    return false;
                }
                return true;
            } else {
                 Log::warning("尝试删除不存在的文件: {$filePath}");
                return true; // 文件本就不存在，视为删除成功
            }
        } catch (\Exception $e) {
            Log::error("删除文件时发生异常: {$filePath} - " . $e->getMessage());
            return false;
        }
    }
} 