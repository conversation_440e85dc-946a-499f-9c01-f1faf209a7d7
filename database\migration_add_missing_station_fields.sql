-- 为 sp_station 表添加缺失字段的迁移脚本
-- 执行日期：2025-06-19
-- 目的：添加目标字段列表中缺失的字段

-- 1. 添加原部室字段
SET @column_exists_1 = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'sp_station'
    AND COLUMN_NAME = 'original_department'
);

SET @sql_1 = IF(@column_exists_1 = 0,
    'ALTER TABLE `sp_station` ADD COLUMN `original_department` varchar(100) DEFAULT NULL COMMENT ''原部室'' AFTER `department_id`',
    'SELECT ''字段 original_department 已存在，跳过添加'' as message'
);

PREPARE stmt1 FROM @sql_1;
EXECUTE stmt1;
DEALLOCATE PREPARE stmt1;

-- 2. 添加项目建设年份字段
SET @column_exists_2 = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'sp_station'
    AND COLUMN_NAME = 'construction_year'
);

SET @sql_2 = IF(@column_exists_2 = 0,
    'ALTER TABLE `sp_station` ADD COLUMN `construction_year` year DEFAULT NULL COMMENT ''项目建设年份'' AFTER `original_department`',
    'SELECT ''字段 construction_year 已存在，跳过添加'' as message'
);

PREPARE stmt2 FROM @sql_2;
EXECUTE stmt2;
DEALLOCATE PREPARE stmt2;

-- 3. 添加异常电站备注字段
SET @column_exists_3 = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'sp_station'
    AND COLUMN_NAME = 'abnormal_remark'
);

SET @sql_3 = IF(@column_exists_3 = 0,
    'ALTER TABLE `sp_station` ADD COLUMN `abnormal_remark` text DEFAULT NULL COMMENT ''异常电站备注（电站状态：拆除、资产减除、电站更换）'' AFTER `fixed_income`',
    'SELECT ''字段 abnormal_remark 已存在，跳过添加'' as message'
);

PREPARE stmt3 FROM @sql_3;
EXECUTE stmt3;
DEALLOCATE PREPARE stmt3;

-- 显示执行结果
SELECT 
    CASE 
        WHEN @column_exists_1 = 0 THEN '✅ 成功添加 original_department 字段'
        ELSE '⚠️ original_department 字段已存在'
    END as result_1,
    CASE 
        WHEN @column_exists_2 = 0 THEN '✅ 成功添加 construction_year 字段'
        ELSE '⚠️ construction_year 字段已存在'
    END as result_2,
    CASE 
        WHEN @column_exists_3 = 0 THEN '✅ 成功添加 abnormal_remark 字段'
        ELSE '⚠️ abnormal_remark 字段已存在'
    END as result_3;

-- 验证字段是否添加成功
SELECT 
    COLUMN_NAME as '字段名',
    DATA_TYPE as '数据类型',
    IS_NULLABLE as '允许空值',
    COLUMN_DEFAULT as '默认值',
    COLUMN_COMMENT as '注释'
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'sp_station' 
AND COLUMN_NAME IN ('original_department', 'construction_year', 'abnormal_remark')
ORDER BY ORDINAL_POSITION;
