// 租金发放管理组件
const RentManagement = {
    template: `
        <div class="rent-management">
            <h2>租金发放管理</h2>
            <!-- 筛选条件 -->
            <div class="filter-section">
                <el-form :inline="true" :model="filterForm">
                    <el-form-item label="时间范围">
                        <el-date-picker
                            v-model="filterForm.dateRange"
                            type="daterange"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            value-format="yyyy-MM-dd">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item label="国网号">
                        <el-input v-model="filterForm.gongwang_account" placeholder="请输入国网号"></el-input>
                    </el-form-item>
                    <el-form-item label="总序号">
                        <el-input v-model="filterForm.total_serial" placeholder="请输入总序号"></el-input>
                    </el-form-item>
                    <el-form-item label="发放方式">
                        <el-select v-model="filterForm.payment_type" placeholder="请选择发放方式">
                            <el-option label="全部" value=""></el-option>
                            <el-option label="银行转账" value="银行转账"></el-option>
                            <el-option label="现金支付" value="现金支付"></el-option>
                            <el-option label="支付宝" value="支付宝"></el-option>
                            <el-option label="微信支付" value="微信支付"></el-option>
                            <el-option label="其他" value="其他"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="handleSearch">查询</el-button>
                        <el-button @click="resetFilter">重置</el-button>
                        <el-button type="success" @click="showRentDialog">登记发放</el-button>
                        <el-button type="primary" @click="handleImport">导入数据</el-button>
                        <el-button type="primary" @click="handleExport">导出数据</el-button>
                    </el-form-item>
                </el-form>
            </div>

            <!-- 数据展示区域 -->
            <div class="data-section">
                <!-- 直接显示数据列表 -->
                <div class="table-section">
                    <el-table :data="tableData" border style="width: 100%" v-loading="tableLoading">
                        <el-table-column prop="total_serial" label="总序号" width="100"></el-table-column>
                        <el-table-column prop="gongwang_account" label="国网号" width="150"></el-table-column>
                        <el-table-column prop="contact_name" label="户名" width="100"></el-table-column>
                        <el-table-column prop="payment_date" label="发放日期" width="120"></el-table-column>
                        <el-table-column prop="payment_type" label="发放方式" width="100">
                            <template slot-scope="scope">
                                <el-tag :type="getPaymentTypeColor(scope.row.payment_type)">
                                    {{ scope.row.payment_type }}
                                </el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column prop="amount" label="发放金额" width="120">
                            <template slot-scope="scope">
                                {{ formatCurrency(scope.row.amount) }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="remark" label="备注说明" min-width="150" show-overflow-tooltip></el-table-column>
                        <el-table-column label="操作" width="150" fixed="right">
                            <template slot-scope="scope">
                                <el-button type="text" size="small" @click="handleEdit(scope.row)">编辑</el-button>
                                <el-button type="text" size="small" @click="handleDelete(scope.row)" style="color: #F56C6C;">删除</el-button>
                            </template>
                        </el-table-column>
                    </el-table>

                    <!-- 分页 -->
                    <div class="pagination-container" style="margin-top: 20px; text-align: right;">
                        <el-pagination
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                            :current-page="currentPage"
                            :page-sizes="[10, 20, 50, 100]"
                            :page-size="pageSize"
                            layout="total, sizes, prev, pager, next, jumper"
                            :total="totalItems">
                        </el-pagination>
                    </div>
                </div>
            </div>

            <!-- 租金发放登记对话框 -->
            <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="600px" @close="resetForm">
                <el-form :model="form" :rules="rules" ref="form" label-width="100px">
                    <el-form-item label="总序号" prop="total_serial">
                        <el-autocomplete
                            ref="totalSerialInput"
                            v-model="form.total_serial"
                            clearable
                            :fetch-suggestions="queryStationsByTotalSerial"
                            placeholder="请输入总序号"
                            @select="handleStationSelectByTotalSerial"
                            @blur="handleSerialBlur"
                            value-key="total_serial"
                            :trigger-on-focus="true"
                            popper-class="station-autocomplete"
                            style="width: 100%;"
                        >
                            <template slot-scope="{ item }">
                                <div style="padding: 5px 0; display: flex; justify-content: space-between; align-items: center;">
                                    <span style="font-weight: bold;">总序号: {{ item.total_serial }}</span>
                                    <span style="color: #666; margin-left: 10px;">联系人: {{ item.contact_name || '无联系人' }}</span>
                                </div>
                            </template>
                        </el-autocomplete>
                    </el-form-item>
                    <el-form-item label="国网号" prop="gongwang_account">
                        <el-input v-model="form.gongwang_account" placeholder="请输入国网号" @blur="handleAccountBlur"></el-input>
                    </el-form-item>
                    <el-form-item label="户名" prop="contact_name">
                        <el-input v-model="form.contact_name" placeholder="请输入户名"></el-input>
                    </el-form-item>
                    <el-form-item label="发放日期" prop="payment_date">
                        <el-date-picker
                            v-model="form.payment_date"
                            type="date"
                            placeholder="选择发放日期"
                            value-format="yyyy-MM-dd"
                            style="width: 100%;">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item label="发放方式" prop="payment_type">
                        <el-select v-model="form.payment_type" placeholder="请选择发放方式" style="width: 100%;">
                            <el-option label="银行转账" value="银行转账"></el-option>
                            <el-option label="现金支付" value="现金支付"></el-option>
                            <el-option label="支付宝" value="支付宝"></el-option>
                            <el-option label="微信支付" value="微信支付"></el-option>
                            <el-option label="其他" value="其他"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="发放金额" prop="amount">
                        <el-input v-model="form.amount" type="number" placeholder="请输入发放金额" @blur="handleAmountBlur">
                            <template slot="append">元</template>
                        </el-input>
                    </el-form-item>
                    <el-form-item label="备注说明" prop="remark">
                        <el-input v-model="form.remark" type="textarea" :rows="3" placeholder="请输入备注信息"></el-input>
                    </el-form-item>
                </el-form>
                <div slot="footer" class="dialog-footer">
                    <el-button @click="dialogVisible = false">取 消</el-button>
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                </div>
            </el-dialog>

            <!-- 电站选择对话框 -->
            <el-dialog title="选择电站" :visible.sync="stationDialogVisible" width="800px">
                <div style="margin-bottom: 20px;">
                    <el-input 
                        v-model="stationSearchKeyword" 
                        placeholder="搜索电站号/国网号/总序号" 
                        style="width: 300px;"
                        @keyup.enter.native="searchStations">
                        <el-button slot="append" icon="el-icon-search" @click="searchStations"></el-button>
                    </el-input>
                </div>
                <el-table :data="stationList" border height="400px" @row-click="selectStation">
                    <el-table-column type="index" label="序号" width="50" align="center"></el-table-column>
                    <el-table-column prop="total_serial" label="总序号" width="100"></el-table-column>
                    <el-table-column prop="gongwang_account" label="国网号" width="150"></el-table-column>
                    <el-table-column prop="contact_name" label="户名" width="120"></el-table-column>
                    <el-table-column prop="address" label="地址" min-width="200" show-overflow-tooltip></el-table-column>
                </el-table>
            </el-dialog>

            <!-- 导入对话框 -->
            <el-dialog title="导入租金发放数据" :visible.sync="importDialogVisible" width="500px">
                <div class="import-content">
                    <p class="import-tips">请上传符合模板格式的Excel文件进行导入。</p>
                    <p class="import-tips">标题格式为：所属部门xxxx年项目租金支付明细表（xxxx年度）</p>
                    <p class="import-tips">必须包含字段：序号、姓名、户号、电话、县区、乡镇、村名、银行卡号、开户银行、应发租金、实发租金、发放状态、备注</p>
                    <p class="import-tips">系统将根据"户号"字段匹配电站信息。</p>
                    
                    <el-upload
                        class="upload-area"
                        drag
                        action="/finance/rent/import"
                        :headers="{ Authorization: 'Bearer ' + localStorage.getItem('token') }"
                        :before-upload="beforeImportUpload"
                        :on-success="handleImportSuccess"
                        :on-error="handleImportError"
                        accept=".xlsx,.xls">
                        <i class="el-icon-upload"></i>
                        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                        <div class="el-upload__tip" slot="tip">仅支持.xlsx、.xls格式，且不超过5MB</div>
                    </el-upload>
                    
                    <div class="template-download">
                        <el-button type="text" @click="downloadTemplate">下载导入模板</el-button>
                    </div>
                </div>
            </el-dialog>
        </div>
    `,
    data() {
        return {
            // 过滤表单
            filterForm: {
                dateRange: [],
                gongwang_account: '',
                total_serial: '',
                payment_type: ''
            },
            // 表格数据
            tableData: [],
            tableLoading: false,
            currentPage: 1,
            pageSize: 10,
            totalItems: 0,
            
            // 对话框控制
            dialogVisible: false,
            dialogTitle: '登记租金发放',
            importDialogVisible: false,
            
            // 表单数据
            form: {
                station_id: null,
                gongwang_account: '',
                total_serial: '',
                contact_name: '',
                payment_date: '',
                payment_type: '',
                amount: '',
                remark: ''
            },
            rules: {
                total_serial: [
                    { required: true, message: '请输入总序号', trigger: 'blur' }
                ],
                gongwang_account: [
                    { required: true, message: '请输入国网号', trigger: 'blur' }
                ],
                contact_name: [
                    { required: true, message: '请输入户名', trigger: 'blur' }
                ],
                payment_date: [
                    { required: true, message: '请选择发放日期', trigger: 'change' }
                ],
                payment_type: [
                    { required: true, message: '请选择发放方式', trigger: 'change' }
                ],
                amount: [
                    { required: true, message: '请输入发放金额', trigger: 'blur' },
                    { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入正确的金额格式', trigger: 'blur' }
                ]
            },
            
            // 电站选择
            stationDialogVisible: false,
            stationList: [],
            stationSearchKeyword: '',
            selectedStation: null,
            stationOptions: [], // 用于自动完成的电站选项
            
            // 上传相关
            uploadHeaders: {
                Authorization: 'Bearer ' + localStorage.getItem('token')
            },
            
            // 新增API交互相关状态
            loading: false,
            importLoading: false,
            submitLoading: false,
            deleteLoading: false,
            stationLoading: false
        };
    },
    mounted() {
        this.fetchData();
        this.fetchStationOptions(); // 初始化电站选项数据
    },
    methods: {
        // 获取租金发放列表数据
        async fetchData() {
            this.tableLoading = true;
            try {
                // 构建请求参数
                const params = {
                    page: this.currentPage,
                    pageSize: this.pageSize
                };
                
                // 添加筛选条件
                if (this.filterForm.dateRange && this.filterForm.dateRange.length === 2) {
                    params.start_date = this.filterForm.dateRange[0];
                    params.end_date = this.filterForm.dateRange[1];
                }
                if (this.filterForm.gongwang_account) {
                    params.gongwang_account = this.filterForm.gongwang_account;
                }
                if (this.filterForm.total_serial) {
                    params.total_serial = this.filterForm.total_serial;
                }
                if (this.filterForm.payment_type) {
                    params.payment_type = this.filterForm.payment_type;
                }
                
                // 调用API
                const response = await this.$http.get('/finance/rent/list', { params });
                
                if (response.data.code === 200) {
                    this.tableData = response.data.data.items;
                    this.totalItems = response.data.data.total;
                } else {
                    this.$message.error(response.data.message || '获取租金发放数据失败');
                }
            } catch (error) {
                console.error('获取租金发放数据异常:', error);
                this.$message.error('获取租金发放数据失败: ' + (error.message || '未知错误'));
            } finally {
                this.tableLoading = false;
            }
        },
        
        getPaymentTypeColor(type) {
            const colors = {
                '银行转账': 'primary',
                '现金支付': 'success',
                '支付宝': 'warning',
                '微信支付': 'info',
                '其他': ''
            };
            return colors[type] || '';
        },
        
        // 显示新增对话框
        showRentDialog() {
            this.dialogTitle = '登记租金发放';
            this.form = {
                station_id: null,
                gongwang_account: '',
                total_serial: '',
                contact_name: '',
                payment_date: '',
                payment_type: '',
                amount: '',
                remark: ''
            };
            this.selectedStation = null;
            this.dialogVisible = true;
        },
        
        // 显示编辑对话框
        handleEdit(row) {
            this.dialogTitle = '编辑租金发放';
            this.form = { ...row };
            this.selectedStation = {
                id: row.station_id,
                total_serial: row.total_serial,
                gongwang_account: row.gongwang_account,
                contact_name: row.contact_name
            };
            this.dialogVisible = true;
        },
        
        // 删除租金发放记录
        async handleDelete(row) {
            try {
                await this.$confirm('确定要删除这条租金发放记录吗？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                });
                
                this.deleteLoading = true;
                const response = await this.$http.delete(`/finance/rent/${row.id}`);
                
                if (response.data.code === 200) {
                    this.$message.success('删除成功');
                    this.fetchData();
                } else {
                    this.$message.error(response.data.message || '删除失败');
                }
            } catch (error) {
                if (error !== 'cancel') {
                    console.error('删除租金发放记录异常:', error);
                    this.$message.error('删除失败: ' + (error.message || '未知错误'));
                }
            } finally {
                this.deleteLoading = false;
            }
        },
        
        // 提交表单
        async submitForm() {
            try {
                // 表单验证
                await this.$refs.form.validate();
                
                this.submitLoading = true;
                let response;
                
                if (this.form.id) {
                    // 更新
                    response = await this.$http.put(`/finance/rent/${this.form.id}`, this.form);
                } else {
                    // 使用save接口进行登记
                    const saveData = {
                        total_serial: this.form.total_serial,
                        gongwang_account: this.form.gongwang_account,
                        contact_name: this.form.contact_name,
                        payment_date: this.form.payment_date,
                        payment_type: this.form.payment_type,
                        amount: parseFloat(this.form.amount),
                        remark: this.form.remark
                    };
                    response = await this.$http.post('/finance/rent/save', saveData);
                }
                
                if (response.data.code === 200) {
                    this.$message.success(this.form.id ? '更新成功' : '登记成功');
                    this.dialogVisible = false;
                    this.fetchData();
                } else {
                    this.$message.error(response.data.message || (this.form.id ? '更新失败' : '登记失败'));
                }
            } catch (error) {
                if (error !== 'cancel') {
                    console.error('提交租金发放表单异常:', error);
                    this.$message.error('提交失败: ' + (error.message || '未知错误'));
                }
            } finally {
                this.submitLoading = false;
            }
        },
        
        // 处理导入
        handleImport() {
            this.importDialogVisible = true;
        },
        
        // 上传前检查
        beforeImportUpload(file) {
            const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || 
                           file.type === 'application/vnd.ms-excel';
            const isLt5M = file.size / 1024 / 1024 < 5;
            
            if (!isExcel) {
                this.$message.error('请上传Excel文件！');
                return false;
            }
            if (!isLt5M) {
                this.$message.error('文件大小不能超过5MB！');
                return false;
            }
            return true;
        },
        
        // 导入成功回调
        handleImportSuccess(response) {
            if (response.code === 0) {
                const data = response.data;
                let message = `导入成功，共处理${data.success + data.fail}条数据。`;
                message += `成功：${data.success}条`;
                
                if (data.fail > 0) {
                    message += `，失败：${data.fail}条`;
                    // 如果有错误信息，在控制台输出详细错误
                    if (data.errors && data.errors.length > 0) {
                        console.error('导入错误详情:', data.errors);
                        // 显示前5条错误信息
                        const errorTips = data.errors.slice(0, 5).join('\n');
                        this.$notify({
                            title: '部分数据导入失败',
                            message: errorTips,
                            type: 'warning',
                            duration: 10000
                        });
                    }
                }
                
                this.$message.success(message);
                this.importDialogVisible = false;
                this.fetchData(); // 刷新数据
            } else {
                this.$message.error(response.msg || '导入失败');
            }
        },
        
        // 导入失败回调
        handleImportError(error) {
            console.error('导入错误:', error);
            let errorMsg = '导入失败';
            if (error.response) {
                try {
                    const res = JSON.parse(error.response);
                    errorMsg = res.msg || errorMsg;
                } catch (e) {
                    errorMsg = '导入失败，服务器返回未知错误';
                }
            }
            this.$message.error(errorMsg);
        },
        
        // 下载导入模板
        downloadTemplate() {
            window.open('/finance/rent/template', '_blank');
        },
        
        // 显示选择电站对话框
        async showStationSelect() {
            this.stationDialogVisible = true;
            await this.searchStations();
        },
        
        // 搜索电站
        async searchStations() {
            this.stationLoading = true;
            try {
                const params = {
                    keyword: this.stationSearchKeyword,
                    page: 1,
                    limit: 10
                };
                
                const response = await this.$http.get('/station/search', { params });
                
                if (response.data.code === 200) {
                    this.stationList = response.data.data.items;
                } else {
                    this.$message.error(response.data.message || '获取电站列表失败');
                }
            } catch (error) {
                console.error('获取电站列表异常:', error);
                this.$message.error('获取电站列表失败: ' + (error.message || '未知错误'));
            } finally {
                this.stationLoading = false;
            }
        },
        
        // 选择电站
        selectStation(row) {
            this.form.station_id = row.id;
            this.selectedStation = row;
            this.stationDialogVisible = false;
        },
        
        // 处理搜索
        handleSearch() {
            this.currentPage = 1;
            this.fetchData();
        },
        
        // 重置筛选条件
        resetFilter() {
            this.filterForm = {
                dateRange: [],
                gongwang_account: '',
                total_serial: '',
                payment_type: ''
            };
            this.currentPage = 1;
            this.fetchData();
        },
        
        // 重置表单
        resetForm() {
            if (this.$refs.form) {
                this.$refs.form.resetFields();
            }
        },
        
        // 分页大小改变
        handleSizeChange(val) {
            this.pageSize = val;
            this.fetchData();
        },
        
        // 当前页改变
        handleCurrentChange(val) {
            this.currentPage = val;
            this.fetchData();
        },
        
        // 格式化金额
        formatCurrency(value) {
            if (!value) return '0.00';
            // 转换为两位小数的金额格式
            const num = parseFloat(value);
            return num.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
        },
        
        // 金额输入框失焦时格式化
        handleAmountBlur() {
            if (this.form.amount) {
                this.form.amount = parseFloat(this.form.amount).toFixed(2);
            }
        },

        // 处理总序号输入，自动查找对应电站信息
        async handleSerialBlur() {
            if (this.form.total_serial && this.form.gongwang_account) {
                await this.autoFillStationInfo();
            }
        },

        // 处理国网号输入，自动查找对应电站信息
        async handleAccountBlur() {
            if (this.form.total_serial && this.form.gongwang_account) {
                await this.autoFillStationInfo();
            }
        },

        // 自动填充电站信息
        async autoFillStationInfo() {
            try {
                const params = {
                    total_serial: this.form.total_serial,
                    gongwang_account: this.form.gongwang_account
                };

                const response = await this.$http.get('/station/search', { params });

                if (response.data.code === 200 && response.data.data.items.length > 0) {
                    const station = response.data.data.items[0];
                    this.form.contact_name = station.contact_name || '';
                    this.form.station_id = station.id;
                    this.selectedStation = station;
                }
            } catch (error) {
                console.error('自动填充电站信息失败:', error);
            }
        },

        // 获取电站选项数据
        async fetchStationOptions() {
            // 如果已经加载过电站数据且有数据，则直接返回缓存数据
            if (this.stationOptions && this.stationOptions.length > 0) {
                console.log('使用缓存的电站数据，跳过API请求');
                return this.stationOptions;
            }

            console.log('开始获取电站数据...');
            try {
                const response = await this.$http.get('/station/problem/index');
                if (response.data.code === 200) {
                    console.log('获取的电站数据示例:', response.data.data[0]);
                    console.log('API返回的电站数据条数:', response.data.data.length);

                    // 确保字段名称一致性
                    this.stationOptions = response.data.data.map(station => {
                        return {
                            ...station,
                            // 统一字段名称，但不替换值
                            total_serial: station.total_serial || '',
                            gongwang_account: station.gongwang_account || station.gongwangAccount || '',
                            contact_name: station.contact_name || station.contactName || '',
                            contact_phone: station.contact_phone || station.contactPhone || ''
                        };
                    });

                    console.log('处理后的电站数据示例:', this.stationOptions[0]);
                    console.log('电站总数:', this.stationOptions.length);

                    // 验证数据中包含total_serial字段的比例
                    const stationsWithTotalSerial = this.stationOptions.filter(station =>
                        station.total_serial && station.total_serial.trim() !== ''
                    ).length;

                    console.log(`包含有效总序号的电站数量: ${stationsWithTotalSerial}/${this.stationOptions.length}`);

                    return this.stationOptions;
                } else {
                    console.error('获取电站数据失败:', response.data.message);
                    this.stationOptions = [];
                    return [];
                }
            } catch (error) {
                console.error('获取电站列表失败:', error);
                this.stationOptions = [];
                return [];
            }
        },

        // 根据总序号查询电站
        queryStationsByTotalSerial(queryString, callback) {
            console.log('查询电站选项，关键词:', queryString);
            console.log('当前所有电站选项数量:', this.stationOptions.length);

            // 添加调试信息验证数据结构
            if (this.stationOptions.length > 0) {
                console.log('第一条电站数据total_serial字段值:', this.stationOptions[0].total_serial);
                console.log('第一条电站数据完整信息:', JSON.stringify(this.stationOptions[0]));
            }

            // 尝试从API重新获取电站数据
            if (this.stationOptions.length === 0) {
                console.log('电站选项为空，重新获取数据');
                this.fetchStationOptions().then(stations => {
                    console.log('重新获取的电站数据数量:', stations.length);
                    this._processQueryResults(queryString, callback);
                }).catch(err => {
                    console.error('重新获取电站数据失败:', err);
                    callback([]);
                });
            } else {
                this._processQueryResults(queryString, callback);
            }
        },

        // 处理查询结果
        _processQueryResults(queryString, callback) {
            // 过滤出匹配的电站，只保留有实际总序号的项目
            let results = [];

            // 首先过滤出有效的total_serial的电站
            const validStations = this.stationOptions.filter(station =>
                station.total_serial && station.total_serial.trim() !== ''
            );

            console.log('有效总序号的电站数量:', validStations.length);

            if (!queryString) {
                // 如果没有查询字符串，显示所有有效总序号的选项
                results = [...validStations];
                console.log('未输入查询条件，返回所有有效总序号选项');
            } else {
                // 有查询字符串时，在有效总序号中进行过滤
                results = validStations.filter(station => {
                    const serialStr = station.total_serial.toString();
                    return serialStr.includes(queryString);
                });
                console.log(`根据条件"${queryString}"过滤后的电站数量:`, results.length);
            }

            // 去除重复的总序号
            const uniqueKeys = new Set();
            results = results.filter(station => {
                if (uniqueKeys.has(station.total_serial)) {
                    return false;
                }
                uniqueKeys.add(station.total_serial);
                return true;
            });

            // 规范化结果对象属性名
            results = results.map(station => {
                // 创建一个新对象避免修改原始数据
                return {
                    id: station.id,
                    total_serial: station.total_serial,
                    gongwang_account: station.gongwang_account || '',
                    // 尝试多种可能的属性名
                    contact_name: station.contact_name || station.contactName || '',
                    contact_phone: station.contact_phone || station.contactPhone || '',
                    value: station.total_serial // 用于显示在输入框中的值
                };
            });

            console.log('处理后的结果数据数量:', results.length);
            if (results.length > 0) {
                console.log('处理后的第一条数据:', results[0]);
            }

            // 限制返回10条结果
            const finalResults = results.slice(0, 10);
            console.log('最终返回的结果数量:', finalResults.length);
            callback(finalResults);
        },

        // 选择电站
        handleStationSelectByTotalSerial(item) {
            console.log('选中的电站数据:', item);

            // 根据选中的总序号找到完整的电站数据
            const selectedStation = this.stationOptions.find(station =>
                station.total_serial === item.total_serial || station.id === item.id
            );

            console.log('找到完整电站数据:', selectedStation);

            if (selectedStation) {
                this.form.station_id = selectedStation.id;
                // 设置表单字段
                this.form.total_serial = selectedStation.total_serial;
                this.form.gongwang_account = selectedStation.gongwang_account || '';

                // 填充联系人信息
                if (selectedStation.contact_name) {
                    this.form.contact_name = selectedStation.contact_name;
                } else if (selectedStation.contactName) {
                    this.form.contact_name = selectedStation.contactName;
                } else {
                    this.form.contact_name = '';
                }

                this.selectedStation = selectedStation;

                console.log('填充后的表单数据:', this.form);
            } else {
                console.warn('未找到选中的电站数据');
            }
        },
        
        // 导出数据
        handleExport() {
            // 构建参数
            const params = {};
            if (this.filterForm.dateRange && this.filterForm.dateRange.length === 2) {
                params.start_date = this.filterForm.dateRange[0];
                params.end_date = this.filterForm.dateRange[1];
            }
            if (this.filterForm.gongwang_account) {
                params.gongwang_account = this.filterForm.gongwang_account;
            }
            if (this.filterForm.total_serial) {
                params.total_serial = this.filterForm.total_serial;
            }
            if (this.filterForm.payment_type) {
                params.payment_type = this.filterForm.payment_type;
            }
            
            // 构建URL参数字符串
            const queryString = Object.keys(params)
                .map(key => encodeURIComponent(key) + '=' + encodeURIComponent(params[key]))
                .join('&');
            
            // 打开导出URL
            window.open(`/finance/rent/export?${queryString}`, '_blank');
        }
    }
};

// 修改导出方式
export default RentManagement;

// 添加自动完成样式
const style = document.createElement('style');
style.textContent = `
.station-autocomplete .el-autocomplete-suggestion__wrap {
    max-height: 300px;
}

.station-autocomplete .el-autocomplete-suggestion__list {
    padding: 0;
}

.station-autocomplete .el-autocomplete-suggestion__list li {
    padding: 8px 12px;
    border-bottom: 1px solid #f0f0f0;
}

.station-autocomplete .el-autocomplete-suggestion__list li:hover {
    background-color: #f5f7fa;
}

.station-autocomplete .el-autocomplete-suggestion__list li:last-child {
    border-bottom: none;
}
`;
document.head.appendChild(style);